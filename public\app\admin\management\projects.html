<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小森活後台 - 專案管理 v1.2.1 (原型-整合折扣)</title>
    <!-- 載入共享樣式 -->
    <link rel="stylesheet" href="../../shared-styles.css">
    <style>
        /* --- 保留之前版本的所有 CSS --- */
        /* --- 從 shared-styles.css 引入的變數 --- */
        /* :root { ... } */
        /* --- 基礎佈局樣式 --- */
        * { margin: 0; padding: 0; box-sizing: border-box; font-family: 'Noto Sans TC', 'Roboto', sans-serif; }
        body { min-height: 100vh; display: flex; background-color: var(--bg-light); color: var(--text-dark); line-height: 1.6; }
        /* ... (側邊欄, 主內容, 頁首, 過濾, 列表, 分頁 CSS 保持不變) ... */
        .sidebar { width: 250px; background-color: var(--bg-white); box-shadow: var(--shadow-md); padding: var(--space-lg) 0; position: fixed; top: 0; left: 0; height: 100vh; overflow-y: auto; z-index: 100; transition: transform 0.3s ease; display: flex; flex-direction: column; }
        .logo-container { padding: 0 var(--space-lg); margin-bottom: var(--space-xl); display: flex; align-items: center; flex-shrink: 0; }
        .logo { width: 40px; margin-right: var(--space-sm); flex-shrink: 0; }
        .logo-text { font-size: 1.2rem; font-weight: 700; color: var(--primary-color); }
        .nav-container { flex-grow: 1; overflow-y: auto; }
        .nav-title { padding: var(--space-sm) var(--space-lg); font-size: 0.75rem; font-weight: 600; color: var(--text-medium); text-transform: uppercase; letter-spacing: 0.5px; margin-top: var(--space-md); }
        .nav-title:first-of-type { margin-top: 0; }
        .nav-item { display: flex; align-items: center; padding: var(--space-md) var(--space-lg); color: var(--text-medium); text-decoration: none; transition: all 0.2s ease; margin-bottom: var(--space-xs); border-right: 3px solid transparent; }
        .nav-item.active { background-color: var(--primary-light); color: var(--primary-color); font-weight: 600; border-right-color: var(--primary-color); }
        .nav-item:hover:not(.active) { background-color: rgba(0, 0, 0, 0.03); }
        .nav-icon { margin-right: var(--space-md); width: 20px; height: 20px; flex-shrink: 0; }
        .main-content { flex: 1; margin-left: 250px; padding: var(--space-xl); transition: margin-left 0.3s ease; }
        .page-header { margin-bottom: var(--space-xl); }
        .header-content { width: 100%; display: flex; justify-content: space-between; align-items: center; gap: var(--space-md); }
        .page-title { font-size: 1.8rem; font-weight: 700; color: var(--text-dark); margin: 0; flex-grow: 1; display: flex; align-items: center; gap: var(--space-md); }
        .header-controls { display: flex; align-items: center; gap: var(--space-md); flex-shrink: 0; }
        .filter-container { background-color: var(--bg-white); padding: var(--space-lg); border-radius: var(--radius-lg); margin-bottom: var(--space-lg); box-shadow: var(--shadow-sm); }
        .filter-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-md); }
        .filter-title { font-size: 1.1rem; font-weight: 600; margin: 0; color: var(--text-dark); }
        .filter-actions { display: flex; gap: var(--space-sm); }
        .filter-row { display: grid; grid-template-columns: 1fr 1fr auto; gap: var(--space-md); align-items: end; }
        .filter-label { display: block; font-size: 0.875rem; margin-bottom: var(--space-xs); color: var(--text-medium); font-weight: 500; }
        .filter-input, .filter-select { width: 100%; padding: var(--space-sm) var(--space-md); border: 1px solid var(--border-color); border-radius: var(--radius-md); background-color: var(--bg-white); color: var(--text-dark); font-size: 0.95rem; transition: border-color 0.2s, box-shadow 0.2s; height: calc(1.6em + 2 * var(--space-sm) + 2px); line-height: 1.6; }
        .filter-input:focus, .filter-select:focus { border-color: var(--primary-color); box-shadow: 0 0 0 3px var(--primary-light); outline: none; }
        .filter-select { appearance: none; background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E"); background-repeat: no-repeat; background-position: right 0.75rem center; background-size: 16px; padding-right: 2.5rem; }
        .filter-group .btn { height: calc(1.6em + 2 * var(--space-sm) + 2px); align-self: end; }
        .project-list { background-color: var(--bg-white); border-radius: var(--radius-lg); box-shadow: var(--shadow-sm); overflow: hidden; margin-bottom: var(--space-lg); }
        .list-header { display: grid; grid-template-columns: 150px minmax(200px, 1fr) 120px 1fr 120px; padding: var(--space-md) var(--space-lg); background-color: var(--bg-light); border-bottom: 1px solid var(--border-color); font-weight: 600; color: var(--text-medium); font-size: 0.9rem; text-transform: uppercase; letter-spacing: 0.5px; }
        .project-row { display: grid; grid-template-columns: 150px minmax(200px, 1fr) 120px 1fr 120px; padding: var(--space-md) var(--space-lg); border-bottom: 1px solid var(--border-color); transition: background-color 0.2s; align-items: center; }
        .project-row:last-child { border-bottom: none; } .project-row:hover { background-color: var(--primary-light); }
        .project-id, .project-name, .project-discount { font-size: 0.95rem; color: var(--text-dark); padding-right: var(--space-md); overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
        .project-id { font-weight: 500; color: var(--text-medium); } .project-name { font-weight: 500; } .project-discount { color: var(--text-medium); }
        .project-status-container { display: flex; }
        .project-actions { display: flex; justify-content: flex-end; gap: var(--space-sm); }
        .action-btn { display: flex; align-items: center; justify-content: center; width: 36px; height: 36px; border-radius: var(--radius-md); background-color: transparent; border: none; color: var(--text-medium); cursor: pointer; transition: all 0.2s; }
        .action-btn svg { width: 18px; height: 18px; }
        .action-btn:hover { background-color: var(--bg-light); color: var(--text-dark); }
        .action-btn.delete:hover { background-color: rgba(231, 76, 60, 0.1); color: var(--danger-color); }
        .pagination { display: flex; justify-content: center; align-items: center; gap: var(--space-sm); margin-top: var(--space-xl); }
        .page-btn { width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; border-radius: var(--radius-md); border: 1px solid var(--border-color); background-color: var(--bg-white); color: var(--text-medium); cursor: pointer; transition: all 0.2s ease; font-weight: 500; }
        .page-btn:hover:not(.active) { border-color: var(--primary-color); color: var(--primary-color); background-color: var(--primary-light); }
        .page-btn.active { background-color: var(--primary-color); color: white; border-color: var(--primary-color); cursor: default; }
        .modal-backdrop { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.6); display: flex; align-items: center; justify-content: center; z-index: 1000; opacity: 0; visibility: hidden; transition: opacity 0.3s ease, visibility 0s linear 0.3s; }
        .modal-backdrop.active { opacity: 1; visibility: visible; transition: opacity 0.3s ease, visibility 0s linear 0s; }
        .modal-container { width: 90%; max-width: 800px; max-height: 90vh; background-color: var(--bg-white); border-radius: var(--radius-lg); box-shadow: var(--shadow-lg); display: flex; flex-direction: column; transform: scale(0.95); opacity: 0; transition: transform 0.3s ease, opacity 0.3s ease; overflow: hidden; }
        .modal-backdrop.active .modal-container { transform: scale(1); opacity: 1; }
        .modal-header { display: flex; align-items: center; justify-content: space-between; padding: var(--space-lg); border-bottom: 1px solid var(--border-color); flex-shrink: 0; }
        .modal-title { font-size: 1.5rem; font-weight: 600; color: var(--text-dark); margin: 0; }
        .modal-close { display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 50%; background-color: transparent; border: none; color: var(--text-medium); cursor: pointer; transition: all 0.2s; }
        .modal-close:hover { background-color: var(--bg-light); color: var(--text-dark); }
        .modal-close svg { width: 24px; height: 24px; }
        .modal-body { position: relative; padding: var(--space-lg); overflow-y: auto; flex-grow: 1; max-height: calc(90vh - 120px); }
        .modal-footer { display: flex; align-items: center; justify-content: flex-end; gap: var(--space-md); padding: var(--space-md) var(--space-lg); border-top: 1px solid var(--border-color); background-color: var(--bg-light); flex-shrink: 0; }
        .form-section { margin-bottom: var(--space-xl); }
        .form-section:last-child { margin-bottom: 0; }
        .form-section-title { display: flex; align-items: center; gap: var(--space-sm); font-size: 1.1rem; font-weight: 600; color: var(--text-dark); margin-bottom: var(--space-lg); padding-bottom: var(--space-sm); border-bottom: 1px solid var(--border-color); }
        .form-section-title svg { width: 20px; height: 20px; color: var(--primary-color); }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: var(--space-lg); margin-bottom: var(--space-lg); }
        .form-row.single-column { grid-template-columns: 1fr; }
        .form-row:last-child { margin-bottom: 0; }
        .form-group { display: flex; flex-direction: column; gap: var(--space-xs); }
        .form-label { font-size: 0.9rem; font-weight: 500; color: var(--text-medium); }
        .form-input, .form-select, .form-textarea { font-size: 0.95rem; padding: var(--space-sm) var(--space-md); border: 1px solid var(--border-color); border-radius: var(--radius-md); background-color: var(--bg-white); color: var(--text-dark); transition: border-color 0.2s, box-shadow 0.2s; width: 100%; }
        .form-input:focus, .form-select:focus, .form-textarea:focus { border-color: var(--primary-color); box-shadow: 0 0 0 3px var(--primary-light); outline: none; }
        .form-textarea { min-height: 100px; resize: vertical; }
        .form-select { appearance: none; background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E"); background-repeat: no-repeat; background-position: right 0.75rem center; background-size: 16px; padding-right: 2.5rem; }
        .form-input:read-only { background-color: var(--bg-light); cursor: not-allowed; opacity: 0.7; }
        .form-help { font-size: 0.8rem; color: var(--text-light); margin-top: var(--space-xs); }
        .image-upload-controls { display: flex; align-items: center; gap: var(--space-md); margin-bottom: var(--space-md); }
        .image-upload-button { padding: var(--space-sm) var(--space-md); }
        .image-preview-list { display: grid; grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)); gap: var(--space-md); margin-top: var(--space-md); padding: var(--space-sm); background-color: var(--bg-light); border-radius: var(--radius-md); min-height: 110px; border: 1px solid var(--border-color); }
        .image-preview-item { position: relative; border-radius: var(--radius-sm); overflow: hidden; border: 1px solid var(--border-color); aspect-ratio: 1 / 1; }
        .image-thumbnail { display: block; width: 100%; height: 100%; object-fit: cover; }
        .image-delete-btn { position: absolute; top: var(--space-xs); right: var(--space-xs); width: 24px; height: 24px; border-radius: 50%; background-color: rgba(0, 0, 0, 0.5); color: white; border: none; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: background-color 0.2s; }
        .image-delete-btn:hover { background-color: var(--danger-color); }
        .image-delete-btn svg { width: 14px; height: 14px; }
        .file-upload-container { border: 2px dashed var(--border-color); border-radius: var(--radius-lg); padding: var(--space-lg); text-align: center; cursor: pointer; transition: border-color 0.2s, background-color 0.2s; margin-bottom: var(--space-lg); background-color: var(--bg-white); }
        .file-upload-container:hover, .file-upload-container.highlight { border-color: var(--primary-color); background-color: var(--primary-light); }
        .file-upload-icon { margin-bottom: var(--space-md); color: var(--text-medium); transition: color 0.2s; }
        .file-upload-icon svg { width: 48px; height: 48px; }
        .file-upload-container:hover .file-upload-icon, .file-upload-container.highlight .file-upload-icon { color: var(--primary-color); }
        .file-upload-text { font-weight: 600; color: var(--text-dark); margin-bottom: var(--space-xs); font-size: 1rem; }
        .file-upload-help { font-size: 0.85rem; color: var(--text-light); }
        .file-list { margin-bottom: var(--space-lg); }
        .file-item { display: flex; align-items: center; gap: var(--space-md); padding: var(--space-md); background-color: var(--bg-light); border-radius: var(--radius-md); margin-bottom: var(--space-sm); border: 1px solid var(--border-color); }
        .file-item:last-child { margin-bottom: 0; }
        .file-item-icon { color: var(--primary-color); flex-shrink: 0; }
        .file-item-icon svg { width: 24px; height: 24px; }
        .file-item-name { flex: 1; font-weight: 500; color: var(--text-dark); font-size: 0.95rem; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
        .file-item-size { font-size: 0.85rem; color: var(--text-medium); flex-shrink: 0; }
        .file-item-remove { display: flex; align-items: center; justify-content: center; width: 32px; height: 32px; border-radius: 50%; background-color: transparent; border: none; color: var(--text-medium); cursor: pointer; transition: all 0.2s; flex-shrink: 0; }
        .file-item-remove:hover { background-color: var(--danger-color); color: white; }
        .file-item-remove svg { width: 16px; height: 16px; }
        .parse-result { margin-top: var(--space-lg); padding: var(--space-lg); background-color: var(--bg-light); border-radius: var(--radius-md); border: 1px solid var(--border-color); }
        .parse-result-title { font-weight: 600; color: var(--text-dark); margin-bottom: var(--space-md); font-size: 1rem; }
        .parse-result-content > div { margin-bottom: var(--space-md) !important; padding: var(--space-md) !important; border: 1px solid var(--border-color) !important; border-radius: var(--radius-md) !important; background-color: var(--bg-white); }
        .parse-result-content > div:last-child { margin-bottom: 0 !important; }
        .parse-result-item { display: grid; grid-template-columns: 80px 1fr; gap: var(--space-sm); margin-bottom: var(--space-xs); font-size: 0.9rem; }
        .parse-result-label { font-weight: 500; color: var(--text-medium); }
        .parse-result-value { color: var(--text-dark); }
        .menu-toggle { display: none; background: none; border: none; color: var(--text-medium); cursor: pointer; padding: var(--space-sm); margin-right: var(--space-sm); }
        .menu-toggle:hover { color: var(--text-dark); }
        .menu-toggle svg { width: 24px; height: 24px; }

        /* --- NEW: 額外樣式 for 折扣設定區塊 --- */
        .discount-definition-section {
            padding: var(--space-lg);
            margin-top: var(--space-lg);
            border: 1px dashed var(--primary-light); /* 用虛線區分 */
            background-color: rgba(var(--primary-light), 0.2); /* 非常淺的背景 */
            border-radius: var(--radius-md);
        }
        .discount-definition-title {
            font-size: 0.95rem;
            font-weight: 600;
            color: var(--primary-color); /* 強調顏色 */
            margin-bottom: var(--space-md);
        }
        .form-checkbox-group { /* 用於 Active Checkbox */
             display: flex;
             align-items: center;
             gap: var(--space-sm);
             margin-top: var(--space-sm); /* 與上方欄位間距 */
         }
         .form-checkbox-group input[type="checkbox"] {
              width: 18px; /* 調整大小 */
              height: 18px;
              cursor: pointer;
          }
         .form-checkbox-group label {
              margin-bottom: 0; /* 移除 label 的下邊距 */
              font-weight: normal; /* 恢復正常字重 */
          }

        /* --- 響應式設計 (保持不變) --- */
        @media (max-width: 992px) {
            .sidebar { width: 220px; } .main-content { margin-left: 220px; }
            .list-header, .project-row { grid-template-columns: 120px minmax(150px, 1fr) 100px 1fr 100px; }
            .filter-row { grid-template-columns: 1fr 1fr; }
            .filter-group:last-child { grid-column: 1 / -1; justify-self: end; }
            .filter-group:last-child .btn { width: auto; }
        }
        @media (max-width: 768px) {
            .sidebar { transform: translateX(-100%); width: 250px; box-shadow: var(--shadow-lg); } .sidebar.active { transform: translateX(0); }
            .main-content { margin-left: 0; } .menu-toggle { display: flex; } .page-title { font-size: 1.5rem; }
            .filter-row { grid-template-columns: 1fr; } .filter-group:last-child { justify-self: stretch; } .filter-group:last-child .btn { width: 100%; }
            .list-header { display: none; } .project-row { display: grid; grid-template-columns: 1fr; gap: var(--space-sm); padding: var(--space-md); position: relative; }
            .project-row > div[data-label]::before { content: attr(data-label) ": "; font-weight: 600; display: inline-block; width: 80px; color: var(--text-medium); margin-right: var(--space-sm); }
            .project-row > div { padding-right: 0; white-space: normal; } .project-status-container::before { width: 80px; } .project-status-container { display: flex; align-items: center; }
            .project-actions { grid-column: 1 / -1; justify-content: flex-start; margin-top: var(--space-sm); } .project-actions::before { display: none; }
            .modal-container { width: 95%; max-height: 95vh; } .form-row { grid-template-columns: 1fr; } .modal-footer { flex-direction: column-reverse; align-items: stretch; } .modal-footer .btn { width: 100%; }
            .image-preview-list { grid-template-columns: repeat(auto-fill, minmax(80px, 1fr)); }
            .discount-definition-section .form-row { grid-template-columns: 1fr; } /* 折扣區塊內強制單欄 */
        }

        /* --- NEW: 專案亮點樣式 --- */
        .highlights-container {
            display: flex;
            flex-direction: column;
            gap: var(--space-md);
        }
        .highlight-item {
            display: grid;
            grid-template-columns: 40px 1fr auto; /* 配合新的按鈕寬度 */
            gap: var(--space-md);
            align-items: start;
            padding: var(--space-md);
            background-color: var(--bg-light);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }
        .highlight-emoji-btn {
            width: 40px; /* 調整為更合適的寬度 */
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            background-color: var(--bg-white);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all 0.2s;
            padding: 0;
            overflow: hidden; /* 改為 hidden */
        }
        .highlight-emoji-btn:hover {
            border-color: var(--primary-color);
            background-color: var(--primary-light);
        }
        .highlight-text {
            flex: 1;
        }
        .highlight-text textarea {
            width: 100%;
            min-height: 40px;
            resize: vertical;
            padding: var(--space-sm) var(--space-md);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 0.95rem;
            line-height: 1.5;
        }
        .highlight-text textarea:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-light);
            outline: none;
        }
        .highlight-remove {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            background: none;
            color: var(--text-medium);
            cursor: pointer;
            border-radius: 50%;
            transition: all 0.2s;
        }
        .highlight-remove:hover {
            background-color: var(--danger-color);
            color: white;
        }
        .emoji-picker {
            position: absolute; /* 改回 absolute */
            z-index: 1000;
            background: var(--bg-white);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-lg);
            padding: var(--space-md);
            width: 320px;
            max-height: 400px;
            overflow-y: auto;
        }
        .emoji-category-title {
            font-size: 0.85rem;
            font-weight: 600;
            color: var(--text-medium);
            padding: var(--space-sm) 0;
            margin-top: var(--space-md);
            border-bottom: 1px solid var(--border-color);
        }
        .emoji-category-title:first-child {
            margin-top: 0;
        }
        .emoji-category-container {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: var(--space-xs);
            padding: var(--space-sm) 0;
        }
        .emoji-picker button {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            background: none;
            border: 1px solid transparent;
            border-radius: var(--radius-sm);
            cursor: pointer;
            transition: all 0.2s;
            padding: 0;
        }
        .emoji-picker button:hover {
            background-color: var(--primary-light);
            border-color: var(--primary-color);
        }
        /* 添加滾動條樣式 */
        .emoji-picker::-webkit-scrollbar {
            width: 6px;
        }
        .emoji-picker::-webkit-scrollbar-track {
            background: var(--bg-light);
            border-radius: 3px;
        }
        .emoji-picker::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }
        .emoji-picker::-webkit-scrollbar-thumb:hover {
            background: var(--text-light);
        }

        /* 載入指示器樣式 */
        .loading-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            color: var(--text-light);
        }

        .loading-indicator.hidden {
            display: none;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 批量操作樣式 */
        .batch-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            margin-bottom: 1rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .batch-info {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .batch-buttons {
            display: flex;
            gap: 0.5rem;
        }

        /* 表格容器樣式 */
        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table th {
            background: var(--bg-light);
            font-weight: 600;
            color: var(--text-dark);
        }

        .data-table tbody tr:hover {
            background: var(--bg-light);
        }

        /* 專案資訊樣式 */
        .project-info {
            display: flex;
            flex-direction: column;
        }

        .project-name {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.25rem;
        }

        .project-id {
            font-size: 0.8rem;
            color: var(--text-light);
        }

        /* 狀態標籤樣式 */
        .project-status {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-ordering_ended {
            background: #fff3cd;
            color: #856404;
        }

        .status-arrived {
            background: #cce5ff;
            color: #004085;
        }

        .status-completed {
            background: #e2e3e5;
            color: #383d41;
        }

        /* 操作按鈕樣式 */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            padding: 0.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .edit-btn {
            background: #e3f2fd;
            color: #1976d2;
        }

        .edit-btn:hover {
            background: #bbdefb;
        }

        .delete-btn {
            background: #ffebee;
            color: #d32f2f;
        }

        .delete-btn:hover {
            background: #ffcdd2;
        }

        /* 分頁樣式 */
        .pagination-container {
            display: flex;
            justify-content: center;
            padding: 1rem;
        }

        .pagination {
            display: flex;
            gap: 0.5rem;
        }

        .pagination-btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            background: white;
            color: var(--text-dark);
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .pagination-btn:hover {
            background: var(--bg-light);
        }

        .pagination-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* 無資料樣式 */
        .no-data {
            text-align: center;
            padding: 3rem;
        }

        .no-data-message {
            color: var(--text-light);
        }

        .no-data-message svg {
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .no-data-message p {
            margin: 0;
            font-size: 1.1rem;
        }

        /* 圖片上傳和預覽樣式 */
        .image-upload-controls {
            margin-bottom: 1rem;
        }

        .image-upload-button {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .image-preview-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .image-preview-item {
            position: relative;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }

        .preview-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            display: block;
        }

        .preview-info {
            padding: 0.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .preview-name {
            font-size: 0.8rem;
            color: var(--text-dark);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            flex: 1;
        }

        .preview-remove {
            background: #ffebee;
            color: #d32f2f;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 16px;
            line-height: 1;
            transition: background-color 0.2s;
        }

        .preview-remove:hover {
            background: #ffcdd2;
        }

        /* 載入狀態樣式 */
        .uploading .image-upload-button {
            opacity: 0.6;
            pointer-events: none;
        }

        .uploading .image-upload-button::after {
            content: '';
            width: 16px;
            height: 16px;
            border: 2px solid var(--border-color);
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 0.5rem;
        }

        /* Toast 通知樣式 */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            color: white !important;
            font-weight: 500;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            font-size: 14px;
            line-height: 1.4;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast-success {
            background: #10b981 !important;
            border-left: 4px solid #059669;
        }

        .toast-error {
            background: #ef4444 !important;
            border-left: 4px solid #dc2626;
        }

        .toast-warning {
            background: #f59e0b !important;
            border-left: 4px solid #d97706;
        }

        .toast-info {
            background: #3b82f6 !important;
            border-left: 4px solid #2563eb;
        }
    </style>
</head>
<body>
    <!-- ... (側邊欄, 主內容 Header, 過濾, 列表, 分頁 HTML 保持不變) ... -->
     <!-- 側邊導航 -->
     <aside class="sidebar">
         <div class="logo-container"> <img src="../../user/forest-life-logo.png" alt="小森活" class="logo"> <div class="logo-text">小森活管理系統</div> </div>
         <div class="nav-container">
             <div class="nav-title">主選單</div>
             <a href="../dashboard.html" class="nav-item"><svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="7" height="9" /><rect x="14" y="3" width="7" height="5" /><rect x="14" y="12" width="7" height="9" /><rect x="3" y="16" width="7" height="5" /></svg>儀表板</a>
             <a href="projects.html" class="nav-item active"><svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" /><rect x="8" y="2" width="8" height="4" rx="1" ry="1" /></svg>專案管理</a>
             <a href="order-management.html" class="nav-item"><svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" /><path d="M14 2v6h6" /><path d="M16 13H8" /><path d="M16 17H8" /><path d="M10 9H8" /></svg>訂單管理</a>
             <a href="user-management.html" class="nav-item"><svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" /><circle cx="12" cy="7" r="4" /></svg>用戶管理</a>
         </div>
     </aside>

     <!-- 主內容 -->
     <main class="main-content">
         <div class="page-header"> <div class="header-content"> <h1 class="page-title">專案管理</h1> <div class="header-controls"> <a href="#" class="btn btn-primary" id="openAddProjectModal"> <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg> 新增專案 </a> <div class="user-control"> <div class="user-avatar"> <img src="../../user/forest-life-logo.png" alt="用戶頭像"> </div> </div> </div> </div> </div>
         <!-- 篩選區域 -->
         <div class="filter-container">
             <div class="filter-header">
                 <h3 class="filter-title">搜尋與過濾</h3>
                 <div class="filter-actions">
                     <button class="btn btn-outline btn-sm" id="resetFilterBtn">重置</button>
                 </div>
             </div>
             <form id="filterForm" class="filter-form">
                 <div class="filter-row">
                     <div class="filter-group">
                         <label class="filter-label" for="projectName">專案名稱</label>
                         <input type="text" id="projectName" name="projectName" class="filter-input" placeholder="輸入專案名稱">
                     </div>
                     <div class="filter-group">
                         <label class="filter-label" for="projectStatus">專案狀態</label>
                         <select id="projectStatus" name="projectStatus" class="filter-select">
                             <option value="">全部狀態</option>
                             <option value="active">進行中</option>
                             <option value="ordering_ended">結束預購</option>
                             <option value="arrived">已到貨</option>
                             <option value="completed">已完成</option>
                         </select>
                     </div>
                     <div class="filter-group">
                         <label class="filter-label" for="startDate">開始日期</label>
                         <input type="date" id="startDate" name="startDate" class="filter-input">
                     </div>
                     <div class="filter-group">
                         <label class="filter-label" for="endDate">結束日期</label>
                         <input type="date" id="endDate" name="endDate" class="filter-input">
                     </div>
                     <div class="filter-group">
                         <button type="submit" class="btn btn-primary">搜尋</button>
                     </div>
                 </div>
             </form>
         </div>

         <!-- 批量操作區域 -->
         <div class="batch-actions">
             <div class="batch-info">
                 <span id="projectCount">載入中...</span>
             </div>
             <div class="batch-buttons">
                 <button class="btn btn-danger btn-sm disabled" id="batchDeleteBtn" disabled>
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                         <polyline points="3,6 5,6 21,6"/>
                         <path d="M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                     </svg>
                     批量刪除
                 </button>
             </div>
         </div>

         <!-- 專案表格 -->
         <div class="table-container">
             <table class="data-table">
                 <thead>
                     <tr>
                         <th width="40">
                             <input type="checkbox" id="selectAllProjects">
                         </th>
                         <th>專案資訊</th>
                         <th>狀態</th>
                         <th>商品數量</th>
                         <th>建立時間</th>
                         <th>更新時間</th>
                         <th width="120">操作</th>
                     </tr>
                 </thead>
                 <tbody id="projectsTableBody">
                     <!-- 專案列表將由 JavaScript 動態載入 -->
                 </tbody>
             </table>
         </div>

         <!-- 載入指示器 -->
         <div id="loadingIndicator" class="loading-indicator hidden">
             <div class="loading-spinner"></div>
             <span>載入中...</span>
         </div>

         <!-- 分頁 -->
         <div id="paginationContainer" class="pagination-container">
             <!-- 分頁將由 JavaScript 動態生成 -->
         </div>
     </main>

    <!-- 新增/編輯專案模態彈窗 -->
    <div class="modal-backdrop" id="addProjectModal">
        <div class="modal-container">
            <div class="modal-header">
                <h2 class="modal-title">新增專案</h2>
                <button class="modal-close" id="closeModal" title="關閉">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                </button>
            </div>
            <div class="modal-body">
                <form id="addProjectForm">
                    <!-- 基本資訊 -->
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" /><rect x="8" y="2" width="8" height="4" rx="1" ry="1" /></svg>
                            基本資訊
                        </h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" for="projectName">專案名稱 *</label>
                                <input type="text" id="projectName" class="form-input" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="projectId">專案 ID (顯示用)</label>
                                <input type="text" id="projectId" class="form-input" placeholder="儲存後自動生成" readonly>
                                <div class="form-help">格式：PYYYYMMDD-NNNN</div>
                            </div>
                        </div>
                        <div class="form-row single-column"> <!-- 狀態移到下方 -->
                             <div class="form-group">
                                <label class="form-label" for="projectDescription">專案描述</label>
                                <textarea id="projectDescription" class="form-textarea" rows="3"></textarea>
                             </div>
                        </div>
                        <!-- 新增：專案亮點設定 -->
                        <div class="form-row single-column">
                            <div class="form-group">
                                <label class="form-label">專案亮點 (最多3個)</label>
                                <div class="highlights-container" id="highlightsContainer">
                                    <!-- 亮點項目將由 JavaScript 動態添加 -->
                                </div>
                                <button type="button" class="btn btn-outline btn-sm" id="addHighlightBtn" style="margin-top: var(--space-md);">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <line x1="12" y1="5" x2="12" y2="19"></line>
                                        <line x1="5" y1="12" x2="19" y2="12"></line>
                                    </svg>
                                    添加亮點
                                </button>
                                <div class="form-help">每個亮點包含一個 Emoji 圖示和對應的說明文字</div>
                             </div>
                        </div>
                         <div class="form-row"> <!-- 狀態放在獨立行 -->
                              <div class="form-group">
                                  <label class="form-label" for="projectStatus">專案狀態 *</label>
                                  <select id="projectStatus" class="form-select" required>
                                      <option value="active">進行中 (Active)</option>
                                      <option value="ordering_ended">結束預購 (Ordering Ended)</option>
                                      <option value="arrived">已到貨 (Arrived)</option>
                                      <option value="completed">已完成 (Completed)</option>
                                  </select>
                              </div>
                              <div class="form-group">
                                  <label class="form-label" for="projectDeadline">預購截止日期</label>
                                  <input type="datetime-local" id="projectDeadline" class="form-input">
                                  <div class="form-help">到期後狀態會自動變更為「結束預購」</div>
                              </div>
                         </div>
                         <div class="form-row">
                             <div class="form-group">
                                 <label class="form-label" for="projectArrivalDate">預計到貨日期</label>
                                 <input type="datetime-local" id="projectArrivalDate" class="form-input">
                                 <div class="form-help">到貨後狀態會自動變更為「已到貨」</div>
                             </div>
                             <div class="form-group">
                                 <!-- 保留空間以保持對齊 -->
                              </div>
                         </div>
                    </div>

                    <!-- 預設折扣設定 -->
                    <div class="form-section">
                        <h3 class="form-section-title">
                             <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"/><line x1="19" y1="5" x2="5" y2="19"/></svg> <!-- 換個圖標 -->
                            預設折扣設定
                        </h3>
                        <div class="form-row single-column"> <!-- 下拉選單單欄 -->
                            <div class="form-group">
                                <label class="form-label" for="projectDefaultDiscount">選擇現有折扣規則</label>
                                <select id="projectDefaultDiscount" class="form-select">
                                    <option value="">-- 無預設折扣 / 或定義下方新規則 --</option>
                                    <!-- 選項應由 JS 動態載入 -->
                                    <option value="discount_uuid_1">早鳥滿5件折50 (D20250401-0001)</option>
                                    <option value="discount_uuid_2">量販滿10件折100 (D20250401-0002)</option>
                                </select>
                                <div class="form-help">選擇一個已建立的折扣規則套用至此專案。如果選擇此項，下方設定將被忽略。</div>
                            </div>
                        </div>

                        <!-- 新增: 定義新折扣區塊 -->
                        <div class="discount-definition-section">
                            <div class="discount-definition-title">或者，直接為此專案定義一個新的數量階梯折扣規則：</div>
                             <div class="form-row">
                                 <div class="form-group">
                                     <label class="form-label" for="newDiscountName">新折扣規則名稱 *</label>
                                     <input type="text" id="newDiscountName" class="form-input" placeholder="例如：專案A滿額折">
                                      <div class="form-help">此名稱將用於標示此折扣規則</div>
                                 </div>
                                 <div class="form-group">
                                     <label class="form-label" for="newDiscountId">新折扣 ID (顯示用)</label>
                                     <input type="text" id="newDiscountId" class="form-input" placeholder="儲存後自動生成" readonly>
                                     <div class="form-help">格式：DYYYYMMDD-NNNN</div>
                                 </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label" for="newDiscountThreshold">數量門檻 * (件)</label>
                                    <input type="number" id="newDiscountThreshold" class="form-input" min="1" step="1" placeholder="例如：10">
                                    <div class="form-help">達到多少件時觸發折扣</div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label" for="newDiscountValue">每件折扣金額 * (元)</label>
                                    <input type="number" id="newDiscountValue" class="form-input" min="0" step="0.01" placeholder="例如：5">
                                     <div class="form-help">達到門檻後，每件商品折扣的金額</div>
                                </div>
                            </div>
                             <div class="form-row single-column">
                                 <div class="form-group">
                                     <label class="form-label" for="newDiscountDescription">新折扣規則描述</label>
                                     <textarea id="newDiscountDescription" class="form-textarea" rows="2" placeholder="例如：單筆訂單購買此專案商品滿10件，每件折5元"></textarea>
                                 </div>
                             </div>
                              <div class="form-row">
                                  <div class="form-group">
                                      <label class="form-label" for="newDiscountStartDate">開始日期 (可選)</label>
                                      <input type="datetime-local" id="newDiscountStartDate" class="form-input">
                                  </div>
                                  <div class="form-group">
                                      <label class="form-label" for="newDiscountEndDate">結束日期 (可選)</label>
                                      <input type="datetime-local" id="newDiscountEndDate" class="form-input">
                                  </div>
                              </div>
                               <div class="form-row single-column">
                                   <div class="form-group">
                                        <div class="form-checkbox-group">
                                            <input type="checkbox" id="newDiscountActive" checked>
                                            <label for="newDiscountActive">啟用此折扣規則</label>
                                        </div>
                                   </div>
                               </div>
                        </div>
                         <div class="form-help" style="margin-top: var(--space-md); text-align: center;">
                             注意：如果上方選擇了現有折扣，這裡定義的新規則將不會被創建或關聯。
                         </div>
                    </div>

                    <!-- 專案圖片管理 -->
                    <div class="form-section">
                         <h3 class="form-section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/></svg>
                            專案圖片管理
                        </h3>
                        <div class="image-upload-controls">
                             <button type="button" class="btn btn-outline image-upload-button" id="uploadProjectImageBtn">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="17 8 12 3 7 8"/><line x1="12" y1="3" x2="12" y2="15"/></svg>
                                 上傳圖片 (模擬)
                             </button>
                             <div class="form-help">點擊按鈕模擬上傳圖片 (將使用 Cloudinary)</div>
                        </div>
                        <div class="image-preview-list" id="imagePreviewList"></div>
                        <div class="form-help" style="margin-top: var(--space-sm);">支援拖曳排序 (未來實現)</div>
                    </div>

                    <!-- 上傳商品清單 -->
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><line x1="10" y1="9" x2="8" y2="9"/></svg>
                            上傳商品清單 (Excel/CSV)
                        </h3>
                        <div class="file-upload-container" id="fileDropArea">
                            <input type="file" id="fileUpload" style="display: none;" accept=".xlsx, .xls, .csv">
                            <div class="file-upload-icon"><svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"><path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242"/><path d="M12 12v9"/><path d="m16 16-4-4-4 4"/></svg></div>
                            <div class="file-upload-text">拖放商品清單檔案到此處或點擊上傳</div>
                            <div class="file-upload-help">支援 Excel (.xlsx, .xls) 或 CSV (.csv)</div>
                        </div>
                        <div class="file-list" id="fileList"></div>
                        <div class="parse-result" id="parseResult" style="display: none;">
                            <div class="parse-result-title">商品清單解析預覽</div>
                            <div class="parse-result-content" id="parseResultContent"></div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" id="cancelAddProject">取消</button>
                <button class="btn btn-primary" id="submitAddProject">建立專案</button>
            </div>
        </div>
    </div>

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <script type="module">
        // 導入專案管理邏輯
        import './../../js/pages/admin/projectManagement.js';
    </script>



</body>
</html>