# 🌲 小森活預購系統

一個基於 LINE LIFF 的預購團購系統，支援多專案管理、訂單處理和用戶管理。

## 🎯 專案狀態

**當前版本**: v1.0.0-beta
**開發狀態**: 核心功能已完成，Supabase + Cloudinary 完整整合
**部署狀態**: 開發環境運行中

### ✅ 已完成功能

#### 🔐 身份驗證與權限
- 管理員身份驗證系統（RPC 函數）
- Row Level Security (RLS) 權限架構
- 多層級權限控制（匿名/用戶/管理員）

#### 📊 資料庫整合
- Supabase PostgreSQL 完整整合
- 所有頁面移除假資料，使用真實資料
- 完整的 RPC 函數架構（繞過 RLS）
- 資料庫連接狀態檢查

#### 🏠 用戶端功能
- 首頁（真實專案資料載入）
- 預購頁面（真實商品資料、購物車功能）
- 訂單歷史（真實訂單資料、狀態追蹤）
- 響應式設計（移動優先）

#### 🎛️ 管理員後台
- 管理員儀表板（真實統計資料）
- 📦 訂單管理系統（完整 CRUD、批量操作、篩選分頁）
- 📁 專案管理系統（完整 CRUD、圖片上傳、批量操作）
- 👥 用戶管理系統（完整 CRUD、角色管理、詳情查看）

#### 🖼️ 圖片上傳功能
- Cloudinary 服務完整整合
- 多圖片批量上傳
- 圖片預覽和管理
- 自動縮圖生成
- 文件驗證和錯誤處理

#### 🔧 技術架構
- ES6 模組化架構
- 服務層封裝（Supabase、Cloudinary、LIFF）
- 統一的錯誤處理和用戶反饋
- Toast 通知系統

### 🚧 進行中功能
- 🔔 LINE 推播通知系統
- 📱 LINE LIFF 用戶身份驗證
- 📊 統計圖表和報表功能

### 🔧 最近修正
- ✅ **RPC 函數衝突修正**：解決 `admin_create_project` 函數重載衝突
- ✅ **資料類型修正**：修正 `images` 欄位從 JSONB 到 TEXT[] 的類型匹配
- ✅ **用戶查詢修正**：修正 `admin_get_all_users` 函數使用正確的 `public.users` 表
- ✅ **用戶管理功能**：修正 `admin_get_user_details` 和 `admin_update_user_role` 函數
- ✅ **專案管理頁面修正**：解決 JavaScript 函數重複定義問題，移除內嵌代碼衝突
- ✅ **環境變數配置修正**：解決瀏覽器環境中無法讀取 Vite 環境變數的語法錯誤
- ✅ **Cloudinary 整合修正**：簡化 `cloudinaryService.js` 配置為直接硬編碼，避免模組載入問題
- ✅ **用戶管理介面優化**：修正頭像閃爍、表格樣式、載入狀態和操作按鈕
- ✅ **用戶刪除功能**：添加軟刪除功能，保護管理員帳戶不被誤刪
- ✅ **專案編輯和刪除功能完整修正**：
  - 新增 `fetchProjectDetails` 和 `admin_get_project_details` RPC 函數
  - 修正 RPC 函數參數類型和資料庫欄位匹配問題
  - 實現完整的表單資料載入和填充功能
  - 支援所有欄位的編輯：基本資訊、日期時間、圖片、亮點、折扣設定
  - **修正刪除功能核心問題**：
    - 修正 `project_status_check` 約束，添加 `deleted` 狀態支援
    - 修正觸發器函數，允許手動設置 `deleted` 狀態
    - 確保軟刪除功能正確執行，已刪除專案不再顯示在列表中
  - **修正編輯表單資料載入問題**：
    - 解決 RPC 函數資料結構解析問題，正確提取 `admin_get_project_details` 屬性中的資料
    - 解決模態框時序問題，確保表單欄位在填充前已完全載入
    - 確保專案名稱和所有欄位正確填充到編輯表單
    - 完善錯誤處理和資料驗證機制
  - 完善的表單驗證和錯誤處理
  - 正確的表單重置和模態框狀態管理
  - 修正 Toast 通知樣式，確保錯誤訊息清晰可見
- ✅ **測試功能驗證**：所有管理員測試功能現已正常運作

## 🏗️ 技術架構

### 前端架構
- **框架**: Vanilla JavaScript (ES6+) + HTML5 + CSS3
- **模組化**: ES6 Modules
- **狀態管理**: 自定義 Store 模式
- **UI 組件**: 原生 DOM 操作 + 工具函數

### 後端架構
- **資料庫**: Supabase PostgreSQL
- **身份驗證**: 自定義管理員驗證 + LINE LIFF
- **API**: Supabase REST API + RPC 函數
- **權限控制**: Row Level Security (RLS)

### 整合服務
- **LINE 平台**: LIFF SDK v2
- **圖片服務**: Cloudinary（上傳、轉換、CDN）
- **部署平台**: Vercel
- **開發工具**: Vite

## 🔐 權限架構設計

### RLS (Row Level Security) 分層
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   匿名用戶      │───▶│  RPC 函數        │───▶│   公開資料      │
│   (瀏覽專案)    │    │  (SECURITY       │    │   (活躍專案)    │
│                 │    │   DEFINER)       │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘

┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   一般用戶      │───▶│  身份驗證 +      │───▶│   個人資料      │
│   (查看訂單)    │    │  RPC 函數        │    │   (個人訂單)    │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘

┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   管理員        │───▶│  管理員驗證 +    │───▶│   全部資料      │
│   (系統管理)    │    │  Admin RPC       │    │   (所有訂單)    │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 已實現的 RPC 函數

#### 公開資料存取
- `get_active_projects()` - 獲取活躍專案（公開）
- `get_project_with_items(project_id)` - 獲取專案及商品（公開）
- `get_project_discounts(project_id)` - 獲取專案折扣（公開）

#### 用戶資料存取
- `get_user_orders(user_id)` - 獲取用戶訂單（需身份驗證）

#### 管理員身份驗證
- `admin_authenticate(admin_id)` - 管理員身份驗證

#### 管理員訂單管理
- `admin_get_all_orders(...)` - 管理員查詢所有訂單
- `admin_get_order_details(order_id)` - 管理員查詢訂單詳情
- `admin_update_order_status(...)` - 管理員更新訂單狀態
- `admin_delete_orders(order_ids)` - 管理員批量刪除訂單

#### 管理員專案管理
- `admin_get_all_projects(...)` - 管理員查詢所有專案
- `admin_create_project(...)` - 管理員創建專案
- `admin_update_project(...)` - 管理員更新專案
- `admin_delete_projects(project_ids)` - 管理員刪除專案（軟刪除）

#### 管理員用戶管理
- `admin_get_all_users(...)` - 管理員查詢所有用戶
- `admin_update_user_role(user_id, role)` - 管理員更新用戶角色
- `admin_get_user_details(user_id)` - 管理員查詢用戶詳情

## 🚀 快速開始

### 環境需求
- Node.js 18+
- npm 或 yarn
- Supabase 帳號
- Cloudinary 帳號（圖片上傳功能）

### 開發環境設置

1. **克隆專案**
```bash
git clone <repository-url>
cd small-forest-life-mix
```

2. **安裝依賴**
```bash
npm install
```

3. **環境變數設定**
```bash
# 複製環境變數範本
cp .env.example .env.local

# 編輯 .env.local，填入以下資訊：
# Supabase 配置
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# LINE LIFF 配置
VITE_LIFF_ID=your_liff_id

# Cloudinary 配置
VITE_CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
VITE_CLOUDINARY_UPLOAD_PRESET=your_cloudinary_upload_preset
```

4. **啟動開發服務器**
```bash
npm run dev
```

5. **訪問應用**
- 用戶端：`http://localhost:3001/app/user/index.html`
- 管理端：`http://localhost:3001/app/admin/admin-login.html`
- 測試頁面：`http://localhost:3001/app/admin/test-orders.html`

### 管理員登入
- 管理員 ID: `admin_test_user`
- 系統會自動驗證並導向管理後台

### 功能測試
- **身份驗證測試**：`/app/admin/auth-test.html`
- **管理功能測試**：`/app/admin/test-orders.html`
- **圖片上傳測試**：專案管理頁面的圖片上傳功能

## 📁 專案結構

```
small-forest-life-mix/
├── public/
│   └── app/
│       ├── user/                    # 用戶端頁面
│       │   ├── index.html          # 首頁（真實專案資料）
│       │   ├── preorder.html       # 預購頁面（購物車功能）
│       │   └── order-history.html  # 訂單歷史（真實訂單資料）
│       ├── admin/                   # 管理端頁面
│       │   ├── admin-login.html    # 管理員登入
│       │   ├── dashboard.html      # 管理儀表板（真實統計）
│       │   ├── test-orders.html    # 功能測試頁面
│       │   ├── auth-test.html      # 身份驗證測試
│       │   └── management/         # 管理功能
│       │       ├── order-management.html    # 訂單管理（完整 CRUD）
│       │       ├── projects.html           # 專案管理（含圖片上傳）
│       │       └── user-management.html    # 用戶管理（角色管理）
│       ├── js/                     # JavaScript 模組
│       │   ├── config/             # 配置文件
│       │   │   └── supabase.js     # Supabase 配置
│       │   ├── services/           # 服務層
│       │   │   ├── supabaseService.js    # Supabase 服務
│       │   │   ├── cloudinaryService.js  # Cloudinary 服務
│       │   │   └── liffService.js        # LINE LIFF 服務
│       │   ├── utils/              # 工具函數
│       │   │   ├── helpers.js      # DOM 操作工具
│       │   │   ├── dateUtils.js    # 日期處理
│       │   │   └── discountCalculator.js # 折扣計算
│       │   ├── stores/             # 狀態管理
│       │   │   ├── cartStore.js    # 購物車狀態
│       │   │   └── authStore.js    # 認證狀態
│       │   └── pages/              # 頁面邏輯
│       │       ├── user/           # 用戶端頁面邏輯
│       │       └── admin/          # 管理端頁面邏輯
│       └── css/                    # 樣式文件
│           ├── global.css          # 全局樣式
│           ├── user.css            # 用戶端樣式
│           └── admin.css           # 管理端樣式
├── src/                            # Vite 入口
├── .env.example                    # 環境變數範本
├── vite.config.js                  # Vite 配置
├── README.md                       # 專案說明（本文件）
├── INTEGRATION_PROGRESS.md         # 整合進度報告
└── package.json                    # 專案依賴
```

## 🔧 開發指南

### 新增頁面流程
1. 在 `public/app/` 下創建 HTML 文件
2. 在 `public/app/js/pages/` 下創建對應的 JavaScript 邏輯
3. 使用 ES6 模組導入所需服務
4. 遵循現有的錯誤處理模式
5. 添加適當的 CSS 樣式

### 新增 RPC 函數
1. 在 Supabase 中創建函數
2. 使用 `SECURITY DEFINER` 確保權限
3. 在 `supabaseService.js` 中添加對應的調用函數
4. 更新權限文檔
5. 進行功能測試

### 圖片上傳功能
1. 使用 `cloudinaryService.js` 進行圖片上傳
2. 支援單張和批量上傳
3. 自動生成縮圖和響應式圖片
4. 包含文件驗證和錯誤處理

### 資料庫操作原則
- 公開資料：使用 RPC 函數繞過 RLS
- 用戶資料：需要身份驗證
- 管理員資料：需要管理員身份驗證
- 所有 RPC 函數使用 `SECURITY DEFINER`

### 狀態管理
- 使用 Nano Stores 進行響應式狀態管理
- 購物車狀態自動持久化到 localStorage
- 認證狀態跨頁面同步
- Toast 通知統一管理

### 測試流程
1. 使用 `/app/admin/test-orders.html` 測試管理功能
2. 使用 `/app/admin/auth-test.html` 測試身份驗證
3. 測試圖片上傳功能
4. 驗證響應式設計

## 📋 待辦事項

### ✅ 已完成功能
- [x] **專案管理系統**：完整 CRUD + 圖片上傳 + 編輯/刪除功能
- [x] **用戶管理系統**：完整 CRUD + 角色管理 + 軟刪除
- [x] **訂單管理頁面**：完整 CRUD + 批量操作
- [x] **Cloudinary 圖片上傳整合**：多圖片上傳、預覽、管理
- [x] **Supabase 資料庫完整整合**：RPC 函數、RLS 政策、觸發器
- [x] **管理員身份驗證系統**：安全的管理員登入和權限控制
- [x] **Toast 通知系統**：統一的成功/錯誤訊息顯示
- [x] **響應式設計**：適配桌面和移動端

### 🔄 當前進行中
- [ ] **專案管理功能完善**：
  - [x] 基本 CRUD 操作
  - [x] 編輯表單資料載入修正
  - [x] 刪除功能核心問題修正
  - [ ] 專案亮點管理功能實現
  - [ ] 折扣規則完整整合
  - [ ] 商品清單上傳和解析功能

### 🔥 下一步高優先級
- [ ] **訂單管理系統完善**：
  - [ ] 訂單詳情彈窗功能
  - [ ] 訂單狀態更新通知（LINE 推播）
  - [ ] 訂單與專案/用戶關聯管理

- [ ] **商品管理功能**：
  - [ ] 商品資料 CRUD 操作
  - [ ] 與專案的關聯管理
  - [ ] 庫存和價格管理

- [ ] **前端用戶介面**：
  - [ ] 用戶端專案瀏覽和訂購功能
  - [ ] 購物車和結帳流程
  - [ ] LINE LIFF 用戶身份驗證整合

### 🔄 中優先級
- [ ] **數據分析和報表**：
  - [ ] 統計圖表和報表功能
  - [ ] 訂單匯出功能（Excel/PDF）
  - [ ] 銷售數據視覺化

- [ ] **系統優化**：
  - [ ] 圖片管理功能（編輯、刪除）
  - [ ] 進階篩選和搜尋功能
  - [ ] 效能優化和快取策略

### 📈 未來規劃
- [ ] **進階功能**：
  - [ ] 多語言支援
  - [ ] 自動化測試
  - [ ] 用戶行為分析
  - [ ] PWA 支援


## 🚀 部署

### Vercel 部署
1. 連接 GitHub 倉庫到 Vercel
2. 設置環境變數
3. 配置構建命令：`npm run build`
4. 設置輸出目錄：`dist`

### 環境變數設置
在 Vercel 中設置以下環境變數：
- `VITE_SUPABASE_URL`
- `VITE_SUPABASE_ANON_KEY`
- `VITE_LIFF_ID`
- `VITE_CLOUDINARY_CLOUD_NAME`
- `VITE_CLOUDINARY_UPLOAD_PRESET`

### 部署檢查清單
- [ ] 環境變數正確設置
- [ ] Supabase RPC 函數已部署
- [ ] Cloudinary 配置正確
- [ ] 管理員帳號已創建
- [ ] 所有頁面功能測試通過

### 部署後驗證
1. 訪問用戶端首頁，確認專案資料載入
2. 測試管理員登入功能
3. 驗證圖片上傳功能
4. 檢查所有管理功能正常運作

## 🤝 貢獻指南

1. Fork 專案
2. 創建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 遵循現有的程式碼風格
4. 更新相關文件
5. 提交變更 (`git commit -m 'Add some AmazingFeature'`)
6. 推送到分支 (`git push origin feature/AmazingFeature`)
7. 開啟 Pull Request

## 📞 支援

### 開發文件
- [整合進度文件](INTEGRATION_PROGRESS.md) - 詳細的功能完成狀態
- [專案說明文件](README.md) - 本文件

### 測試頁面
- [身份驗證測試](http://localhost:3001/app/admin/auth-test.html)
- [管理功能測試](http://localhost:3001/app/admin/test-orders.html)
- [用戶端首頁](http://localhost:3001/app/user/index.html)

### 常見問題
1. **無法載入資料**：檢查 Supabase 連接和環境變數
2. **圖片上傳失敗**：檢查 Cloudinary 配置
3. **管理員登入失敗**：確認管理員帳號已創建
4. **頁面載入錯誤**：檢查瀏覽器控制台錯誤訊息

### 技術支援
- 檢查 `INTEGRATION_PROGRESS.md` 了解最新功能狀態
- 使用測試頁面驗證功能
- 查看瀏覽器開發者工具的錯誤訊息

## 📄 授權

此專案採用 MIT 授權 - 詳見 [LICENSE](LICENSE) 文件


