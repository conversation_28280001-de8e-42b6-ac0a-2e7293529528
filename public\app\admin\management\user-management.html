<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小森活後台 - 用戶管理</title>
    <link rel="stylesheet" href="../../shared-styles.css">
    <style>
        /* 基本設置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Noto Sans TC', 'Roboto', sans-serif;
        }

        body {
            min-height: 100vh;
            display: flex;
        }

        /* 側邊導航 */
        .sidebar {
            width: 250px;
            background-color: var(--bg-white);
            box-shadow: var(--shadow-md);
            padding: var(--space-lg) 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 10;
        }

        .logo-container {
            padding: 0 var(--space-lg);
            margin-bottom: var(--space-xl);
            display: flex;
            align-items: center;
        }

        .logo {
            width: 40px;
            margin-right: var(--space-sm);
        }

        .logo-text {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .nav-title {
            padding: var(--space-sm) var(--space-lg);
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--text-medium);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: var(--space-md) var(--space-lg);
            color: var(--text-medium);
            text-decoration: none;
            transition: all 0.2s ease;
            margin-bottom: var(--space-xs);
        }

        .nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            font-weight: 500;
            border-right: 3px solid var(--primary-color);
        }

        .nav-item:hover:not(.active) {
            background-color: rgba(0, 0, 0, 0.03);
        }

        .nav-icon {
            margin-right: var(--space-md);
            width: 20px;
            height: 20px;
        }

        /* 主内容 */
        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: var(--space-xl);
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-xl);
        }

        .page-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-dark);
        }

        /* 過濾區域樣式 */
        .filter-container {
            background: var(--bg-white);
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
            margin-bottom: var(--space-lg);
            box-shadow: var(--shadow-sm);
        }

        .filter-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-md);
        }

        .filter-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
        }

        .filter-row {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .filter-group {
            flex: 1;
        }

        .filter-label {
            display: block;
            margin-bottom: var(--space-xs);
            font-size: 0.9rem;
            color: var(--text-medium);
        }

        .filter-input,
        .filter-select {
            width: 100%;
            padding: var(--space-sm);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 1rem;
            transition: all 0.2s ease;
        }

        .filter-input:focus,
        .filter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-light);
        }

        .filter-select {
            /* Inherits standardized styles from .filter-input */
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 16px;
        }

        /* 用戶列表樣式 */
        .user-list {
            background: var(--bg-white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
        }

        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
            vertical-align: middle;
        }

        .data-table th {
            background: var(--bg-light);
            font-weight: 600;
            color: var(--text-dark);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .data-table tbody tr:hover {
            background: var(--bg-light);
        }

        .data-table tbody tr:last-child td {
            border-bottom: none;
        }

        /* 用戶資訊樣式 */
        .user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .user-avatar {
            position: relative;
            flex-shrink: 0;
        }

        .user-avatar img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #e5e7eb;
            background-color: #f3f4f6;
            transition: none; /* 移除過渡效果避免閃爍 */
        }

        .user-avatar img.loading {
            opacity: 0.5;
        }

        .user-avatar img.error {
            background-color: #f3f4f6;
            border-color: #d1d5db;
        }

        .user-display-name {
            display: flex;
            flex-direction: column;
        }

        .community-nickname {
            font-weight: 500;
            color: var(--text-dark);
        }

        .line-name {
            font-size: 0.85rem;
            color: var(--text-medium);
        }

        /* 角色標籤樣式 */
        .user-role {
            display: inline-flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .role-admin {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        .role-manager {
            background: #fffbeb;
            color: #d97706;
            border: 1px solid #fed7aa;
        }

        .role-user {
            background: #f0f9ff;
            color: #0284c7;
            border: 1px solid #bae6fd;
        }

        /* 操作按鈕樣式 */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .action-btn {
            width: 36px;
            height: 36px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8fafc;
            color: #64748b;
            border: 1px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background-color: #e2e8f0;
            color: #334155;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .action-btn.view-btn:hover {
            background-color: #dbeafe;
            color: #1d4ed8;
            border-color: #93c5fd;
        }

        .action-btn.edit-btn:hover {
            background-color: #fef3c7;
            color: #d97706;
            border-color: #fcd34d;
        }

        .action-btn.delete-btn {
            background-color: #fef2f2;
            color: #dc2626;
            border-color: #fecaca;
        }

        .action-btn.delete-btn:hover {
            background-color: #fee2e2;
            color: #b91c1c;
            border-color: #fca5a5;
        }

        /* 用戶詳情對話框樣式 */
        .user-detail-dialog {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none; /* 預設隱藏 */
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .dialog-content {
            background: var(--bg-white);
            border-radius: var(--radius-lg);
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            animation: dialogFadeIn 0.3s ease;
            box-shadow: var(--shadow-lg);
        }

        @keyframes dialogFadeIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dialog-header {
            position: sticky;
            top: 0;
            background: var(--bg-white);
            padding: var(--space-lg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 1;
        }

        .dialog-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--text-medium);
            cursor: pointer;
        }

        .dialog-body {
            padding: var(--space-lg);
        }

        .user-profile-header {
            display: flex;
            gap: var(--space-lg);
            margin-bottom: var(--space-xl);
        }

        .large-avatar img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: var(--space-md);
            margin-bottom: var(--space-xl);
        }

        .info-item {
            padding: var(--space-md);
            background: var(--bg-light);
            border-radius: var(--radius-md);
        }

        .info-label {
            font-size: 0.85rem;
            color: var(--text-medium);
            margin-bottom: var(--space-xs);
        }

        .info-value {
            font-weight: 500;
            color: var(--text-dark);
        }

        .role-edit-section,
        .nickname-edit-section,
        .order-history-section {
            margin-top: var(--space-xl);
            padding-top: var(--space-xl);
            border-top: 1px solid var(--border-color);
        }

        .role-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-md);
            margin-top: var(--space-md);
        }

        .role-option {
            padding: var(--space-md);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all 0.2s;
            background: var(--bg-white);
        }

        .role-option.active {
            border-color: var(--primary-color);
            background: var(--primary-light);
            box-shadow: 0 0 0 1px var(--primary-color);
        }

        .role-icon {
            font-size: 1.5rem;
            margin-bottom: var(--space-sm);
        }

        .role-name {
            font-weight: 600;
            margin-bottom: var(--space-xs);
        }

        .role-desc {
            font-size: 0.85rem;
            color: var(--text-medium);
        }

        .form-group {
            margin-top: var(--space-md);
        }

        .form-label {
            display: block;
            margin-bottom: var(--space-sm);
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: var(--space-sm);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
        }

        .order-mini-list {
            margin-top: var(--space-md);
        }

        .order-mini-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr;
            padding: var(--space-sm);
            border-bottom: 1px solid var(--border-color);
        }

        .order-mini-id {
            font-weight: 500;
            color: var(--text-dark);
        }

        .order-mini-amount {
            text-align: right;
            font-weight: 500;
            color: var(--text-dark);
        }

        .dialog-footer {
            position: sticky;
            bottom: 0;
            background: var(--bg-white);
            padding: var(--space-lg);
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: var(--space-md);
            z-index: 1;
        }

        /* 按鈕樣式 */
        /* 按鈕樣式 (Standardized) */
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 1rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-icon {
            margin-right: 0.5rem;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 2px 6px var(--primary-shadow);
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            box-shadow: 0 4px 12px var(--primary-shadow);
        }

        .btn-outline {
            background-color: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-outline:hover {
            background-color: var(--primary-light);
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.85rem;
        }

        /* 載入和空狀態樣式 */
        .loading-indicator {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 3rem;
            background: white;
            border-radius: 8px;
            margin: 1rem 0;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f4f6;
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .no-data {
            text-align: center;
            padding: 3rem;
            color: var(--text-medium);
        }

        .no-data-message {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
        }

        .no-data-message svg {
            color: #d1d5db;
        }

        .no-data-message p {
            font-size: 1.1rem;
            margin: 0;
        }

        .hidden {
            display: none !important;
        }

        /* 分頁樣式 */
        .pagination-container {
            margin-top: 1.5rem;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
        }

        .pagination-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #e2e8f0;
            background: white;
            color: #64748b;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.9rem;
        }

        .pagination-btn:hover {
            background: #f8fafc;
            border-color: #cbd5e1;
        }

        .pagination-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 批量操作樣式 */
        .batch-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .batch-info {
            font-size: 0.9rem;
            color: var(--text-medium);
        }

        /* 響應式設計 */
        @media (max-width: 992px) {
            .sidebar {
                width: 200px;
            }

            .main-content {
                margin-left: 200px;
            }

            .list-header, .user-row {
                grid-template-columns: 50px 1fr 120px 100px;
            }

            .user-id, .last-login {
                display: none;
            }

            .dialog-content {
                width: 95%;
            }

            .info-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 1rem;
            }

            .list-header, .user-row {
                grid-template-columns: 50px 1fr 80px;
                padding: 0.75rem;
                font-size: 0.9rem;
            }

            .user-id, .last-login, .user-role {
                display: none;
            }

            .filter-row {
                flex-direction: column;
            }

            .filter-group {
                width: 100%;
            }

            .dialog-content {
                width: 100%;
                height: 100%;
                max-height: 100vh;
                border-radius: 0;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .role-options {
                grid-template-columns: 1fr;
            }
        }

        /* 管理員專屬區域樣式 */
        .admin-only {
            position: relative;
        }

        .role-notice {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            padding: var(--space-sm);
            background: var(--warning-bg);
            border-radius: var(--radius-sm);
            margin: var(--space-md) 0;
            color: var(--warning-text);
            font-size: 0.9rem;
        }

        .role-notice svg {
            flex-shrink: 0;
            color: var(--warning-text);
        }

        /* 備註區塊樣式 */
        .note-edit-section {
            margin-top: var(--space-xl);
            padding-top: var(--space-xl);
            border-top: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: var(--space-md);
        }

        textarea.form-input {
            resize: vertical;
            min-height: 80px;
            font-family: inherit;
            line-height: 1.5;
        }

        /* Adjust grid for three role options */
        .role-options {
             grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); /* Adjusted minmax for potentially 3 items */
        }

        /* 更新表單樣式 */
        .form-group + .form-group {
            margin-top: 1.5rem;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-light);
        }

        /* 非管理員時的樣式 */
        .role-edit-section.disabled {
            opacity: 0.7;
            pointer-events: none;
        }

        .role-edit-section.disabled::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.5);
            cursor: not-allowed;
        }

        /* Toast 通知樣式 */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 9999;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast-success {
            background-color: #10b981;
        }

        .toast-error {
            background-color: #ef4444;
        }

        .toast-info {
            background-color: #3b82f6;
        }

        .toast-warning {
            background-color: #f59e0b;
        }
    </style>
</head>
<body>
    <!-- 側邊導航 -->
    <aside class="sidebar">
        <div class="logo-container">
            <img src="../../user/forest-life-logo.png" alt="小森活" class="logo">
            <div class="logo-text">小森活管理系統</div>
        </div>

        <div class="nav-title">主選單</div>
        <a href="../dashboard.html" class="nav-item">
            <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="7" height="9" />
                <rect x="14" y="3" width="7" height="5" />
                <rect x="14" y="12" width="7" height="9" />
                <rect x="3" y="16" width="7" height="5" />
            </svg>
            儀表板
        </a>
        <a href="projects.html" class="nav-item">
            <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" />
                <rect x="8" y="2" width="8" height="4" rx="1" ry="1" />
            </svg>
            專案管理
        </a>
        <a href="order-management.html" class="nav-item">
            <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                <path d="M14 2v6h6" />
                <path d="M16 13H8" />
                <path d="M16 17H8" />
                <path d="M10 9H8" />
            </svg>
            訂單管理
        </a>
        <a href="user-management.html" class="nav-item active">
            <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
                <circle cx="12" cy="7" r="4" />
            </svg>
            用戶管理
        </a>
    </aside>

    <!-- 主內容 -->
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">用戶管理</h1>
            <div class="user-control">
                <div class="user-avatar">
                    <img src="../../user/forest-life-logo.png" alt="用戶頭像">
                </div>
            </div>
        </div>

        <!-- 搜尋與過濾 -->
        <div class="filter-container">
            <div class="filter-header">
                <h3 class="filter-title">搜尋與過濾</h3>
                <div class="filter-actions">
                    <button class="btn btn-outline btn-sm" id="resetFilterBtn">重置</button>
                </div>
            </div>

            <form id="filterForm" class="filter-form">
                <div class="filter-row">
                    <div class="filter-group">
                        <label class="filter-label" for="displayName">用戶名稱</label>
                        <input type="text" id="displayName" name="displayName" class="filter-input" placeholder="輸入社群暱稱或LINE名稱">
                    </div>

                    <div class="filter-group">
                        <label class="filter-label" for="userId">用戶ID</label>
                        <input type="text" id="userId" name="userId" class="filter-input" placeholder="輸入LINE用戶ID">
                    </div>

                    <div class="filter-group">
                        <label class="filter-label" for="role">用戶角色</label>
                        <select id="role" name="role" class="filter-select">
                            <option value="">全部角色</option>
                            <option value="admin">系統管理員</option>
                            <option value="manager">管理者</option>
                            <option value="user">一般用戶</option>
                        </select>
                    </div>
                </div>

                <div class="filter-row">
                    <div class="filter-group">
                        <label class="filter-label" for="startDate">開始日期</label>
                        <input type="date" id="startDate" name="startDate" class="filter-input">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label" for="endDate">結束日期</label>
                        <input type="date" id="endDate" name="endDate" class="filter-input">
                    </div>
                    <div class="filter-group">
                        <button type="submit" class="btn btn-primary">搜尋</button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 批量操作區域 -->
        <div class="batch-actions">
            <div class="batch-info">
                <span id="userCount">載入中...</span>
            </div>
        </div>

        <!-- 用戶表格 -->
        <div class="table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th width="40">
                            <input type="checkbox" id="selectAllUsers">
                        </th>
                        <th>用戶資訊</th>
                        <th>用戶ID</th>
                        <th>角色</th>
                        <th>最後登入</th>
                        <th width="120">操作</th>
                    </tr>
                </thead>
                <tbody id="usersTableBody">
                    <!-- 用戶列表將由 JavaScript 動態載入 -->
                </tbody>
            </table>
        </div>

        <!-- 載入指示器 -->
        <div id="loadingIndicator" class="loading-indicator hidden">
            <div class="loading-spinner"></div>
            <span>載入中...</span>
        </div>

        <!-- 分頁 -->
        <div id="paginationContainer" class="pagination-container">
            <!-- 分頁將由 JavaScript 動態生成 -->
        </div>

        <!-- 用戶詳情對話框 -->
        <div class="user-detail-dialog" style="display: none;">
            <div class="dialog-content">
                <div class="dialog-header">
                    <h2 class="dialog-title">用戶詳情</h2>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="dialog-body">
                    <div class="user-info-section">
                        <div class="user-profile-header">
                            <div class="large-avatar">
                                <img src="/api/placeholder/80/80" alt="用戶頭像">
                            </div>
                            <div class="profile-details">
                                <h3 class="user-full-name">王小明</h3>
                                <div class="user-role role-admin">系統管理員</div>
                            </div>
                        </div>

                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">LINE 名稱</div>
                                <div class="info-value">wang_user</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">社群暱稱</div>
                                <div class="info-value">王小明</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">LINE ID</div>
                                <div class="info-value">U123456789</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">最後登入</div>
                                <div class="info-value">2025/04/01 10:30</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">註冊時間</div>
                                <div class="info-value">2025/01/01</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">備註</div>
                                <div class="info-value">活動積極參與者，對永續議題很有興趣</div>
                            </div>
                        </div>
                    </div>

                    <!-- 角色編輯區塊 (僅管理員可見) -->
                    <div class="role-edit-section admin-only">
                        <h3 class="role-edit-title">角色設定</h3>
                        <div class="role-notice">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="12" y1="8" x2="12" y2="12"></line>
                                <line x1="12" y1="16" x2="12.01" y2="16"></line>
                            </svg>
                            <span>只有系統管理員可以變更用戶角色</span>
                        </div>
                        <div class="role-options">
                             <div class="role-option"> <!-- Added Admin Role Option -->
                                <div class="role-icon">👑</div>
                                <div class="role-name">系統管理員</div>
                                <div class="role-desc">最高權限</div>
                            </div>
                            <div class="role-option">
                                <div class="role-icon">⭐</div>
                                <div class="role-name">管理者</div>
                                <div class="role-desc">可管理專案和訂單</div>
                            </div>
                            <div class="role-option active">
                                <div class="role-icon">👤</div>
                                <div class="role-name">一般用戶</div>
                                <div class="role-desc">基本使用權限</div>
                            </div>
                        </div>
                    </div>

                    <!-- 備註編輯區塊 -->
                    <div class="note-edit-section">
                        <h3 class="section-title">備註設定</h3>
                        <div class="form-group">
                            <label class="form-label">社群暱稱</label>
                            <input type="text" class="form-input" value="王小明">
                        </div>
                        <div class="form-group">
                            <label class="form-label">管理備註</label>
                            <textarea class="form-input" rows="3" placeholder="輸入對此用戶的備註...">活動積極參與者，對永續議題很有興趣</textarea>
                        </div>
                    </div>

                    <!-- 訂單歷史區塊 -->
                    <div class="order-history-section">
                        <h3 class="history-title">最近訂單</h3>
                        <div class="order-mini-list">
                            <div class="order-mini-row">
                                <div class="order-mini-id">#O20250401-0023</div>
                                <div class="order-mini-date">2025/04/01</div>
                                <div class="order-mini-amount">$1,280</div>
                            </div>
                            <div class="order-mini-row">
                                <div class="order-mini-id">#O20250331-0156</div>
                                <div class="order-mini-date">2025/03/31</div>
                                <div class="order-mini-amount">$2,460</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="dialog-footer">
                    <button class="btn btn-outline">取消</button>
                    <button class="btn btn-primary">儲存變更</button>
                </div>
            </div>
        </div>
    </main>

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <script type="module">
        // 導入用戶管理邏輯
        import './../../js/pages/admin/userManagement.js';
    </script>
</body>
</html>
