# 🛠️ 小森活預購系統 - 完整開發指引

這是小森活預購系統的完整開發指引文件，包含所有技術細節、架構設計、開發流程和最佳實踐。

## 🎯 專案概述

### 系統架構
- **前端**: HTML + CSS + Vanilla JavaScript (ES6 模組)
- **後端**: Supabase (PostgreSQL + RPC 函數 + RLS)
- **圖片服務**: Cloudinary
- **身份驗證**: LINE LIFF + 自定義管理員驗證
- **開發工具**: Vite
- **部署**: Vercel

### 混合架構設計
- **用戶端** (`/app/user/`): 響應式網頁，支援 LINE 內瀏覽器
- **管理端** (`/app/admin/`): 完整的後台管理系統
- **服務層**: 模組化的 JavaScript 服務
- **狀態管理**: Nano Stores 響應式狀態

## 🏗️ 技術架構詳解

### 前端架構
```
public/app/
├── user/                    # 用戶端頁面
│   ├── index.html          # 首頁（專案列表）
│   ├── preorder.html       # 預購頁面（商品選擇）
│   └── order-history.html  # 訂單歷史
├── admin/                   # 管理端頁面
│   ├── admin-login.html    # 管理員登入
│   ├── dashboard.html      # 管理儀表板
│   ├── test-orders.html    # 功能測試頁面
│   └── management/         # 管理功能
│       ├── order-management.html    # 訂單管理
│       ├── projects.html           # 專案管理
│       └── user-management.html    # 用戶管理
└── js/                     # JavaScript 模組
    ├── config/             # 配置文件
    ├── services/           # 服務層
    ├── utils/              # 工具函數
    ├── stores/             # 狀態管理
    └── pages/              # 頁面邏輯
```

### 服務層架構
```javascript
// 核心服務模組
services/
├── supabaseService.js      # 資料庫操作
├── cloudinaryService.js    # 圖片上傳
├── liffService.js          # LINE LIFF 整合
└── authService.js          # 身份驗證

// 工具模組
utils/
├── helpers.js              # DOM 操作工具
├── dateUtils.js            # 日期處理
├── discountCalculator.js   # 折扣計算
└── domUtils.js             # DOM 工具

// 狀態管理
stores/
├── cartStore.js            # 購物車狀態
└── authStore.js            # 認證狀態
```

## 🔐 權限架構設計

### RLS (Row Level Security) 分層
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   匿名用戶      │───▶│  RPC 函數        │───▶│   公開資料      │
│   (瀏覽專案)    │    │  (SECURITY       │    │   (活躍專案)    │
│                 │    │   DEFINER)       │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘

┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   一般用戶      │───▶│  身份驗證 +      │───▶│   個人資料      │
│   (查看訂單)    │    │  RPC 函數        │    │   (個人訂單)    │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘

┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   管理員        │───▶│  管理員驗證 +    │───▶│   全部資料      │
│   (系統管理)    │    │  Admin RPC       │    │   (所有訂單)    │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 權限分層原則
1. **公開資料**: 活躍專案、專案詳情 - 使用 RPC 函數繞過 RLS
2. **用戶資料**: 個人訂單 - 需要用戶身份驗證
3. **管理員資料**: 所有訂單、統計資料 - 需要管理員身份驗證

### 安全機制
- 所有 RPC 函數使用 `SECURITY DEFINER` 以定義者權限執行
- 管理員身份驗證通過 `admin_authenticate` RPC 函數驗證
- 前端使用 localStorage 暫存管理員身份（開發用）
- 所有敏感操作都需要重新驗證管理員權限

## 📊 RPC 函數完整列表

### 公開資料存取
```sql
-- 獲取活躍專案（公開）
get_active_projects()

-- 獲取專案及商品（公開）
get_project_with_items(project_id UUID)

-- 獲取專案折扣（公開）
get_project_discounts(project_id UUID)
```

### 用戶資料存取
```sql
-- 獲取用戶訂單（需身份驗證）
get_user_orders(user_id TEXT)
```

### 管理員身份驗證
```sql
-- 管理員身份驗證
admin_authenticate(admin_id TEXT)
```

### 管理員訂單管理
```sql
-- 管理員查詢所有訂單
admin_get_all_orders(
  p_limit INTEGER DEFAULT 20,
  p_offset INTEGER DEFAULT 0,
  p_order_number TEXT DEFAULT NULL,
  p_user_name TEXT DEFAULT NULL,
  p_project_name TEXT DEFAULT NULL,
  p_status TEXT DEFAULT NULL,
  p_start_date DATE DEFAULT NULL,
  p_end_date DATE DEFAULT NULL
)

-- 管理員查詢訂單詳情
admin_get_order_details(order_id UUID)

-- 管理員更新訂單狀態
admin_update_order_status(
  order_id UUID,
  new_status TEXT,
  admin_notes TEXT DEFAULT NULL
)

-- 管理員批量刪除訂單
admin_delete_orders(order_ids UUID[])
```

### 管理員專案管理
```sql
-- 管理員查詢所有專案
admin_get_all_projects(
  p_limit INTEGER DEFAULT 20,
  p_offset INTEGER DEFAULT 0,
  p_name TEXT DEFAULT NULL,
  p_status TEXT DEFAULT NULL,
  p_start_date DATE DEFAULT NULL,
  p_end_date DATE DEFAULT NULL
)

-- 管理員查詢專案詳情（新增）
admin_get_project_details(p_project_id TEXT)

-- 管理員創建專案
admin_create_project(
  p_name TEXT,
  p_description TEXT,
  p_project_status TEXT,
  p_images TEXT[],
  p_highlights JSONB,
  p_items JSONB,
  p_default_discount_id UUID,
  p_deadline TIMESTAMP,
  p_arrival_date TIMESTAMP
)

-- 管理員更新專案
admin_update_project(
  p_project_id TEXT,
  p_name TEXT,
  p_description TEXT,
  p_project_status TEXT,
  p_images TEXT[],
  p_highlights JSONB,
  p_items JSONB,
  p_default_discount_id UUID,
  p_deadline TIMESTAMP,
  p_arrival_date TIMESTAMP
)

-- 管理員刪除專案（軟刪除）
admin_delete_projects(project_ids UUID[])
```

### 管理員用戶管理
```sql
-- 管理員查詢所有用戶
admin_get_all_users(
  p_limit INTEGER DEFAULT 20,
  p_offset INTEGER DEFAULT 0,
  p_user_id TEXT DEFAULT NULL,
  p_display_name TEXT DEFAULT NULL,
  p_role TEXT DEFAULT NULL,
  p_start_date DATE DEFAULT NULL,
  p_end_date DATE DEFAULT NULL
)

-- 管理員更新用戶角色
admin_update_user_role(user_id TEXT, new_role TEXT)

-- 管理員查詢用戶詳情
admin_get_user_details(user_id TEXT)
```

## 🔧 開發流程與最佳實踐

### 新增頁面流程
1. **創建 HTML 文件**
   ```bash
   # 在對應目錄創建 HTML 文件
   touch public/app/user/new-page.html
   # 或
   touch public/app/admin/management/new-feature.html
   ```

2. **創建 JavaScript 邏輯**
   ```bash
   # 在 pages 目錄創建對應的 JS 文件
   touch public/app/js/pages/user/newPage.js
   # 或
   touch public/app/js/pages/admin/newFeature.js
   ```

3. **模組導入結構**
   ```javascript
   // 標準的模組導入結構
   import { supabaseService } from '../../services/supabaseService.js';
   import { showToast, showLoading } from '../../utils/helpers.js';
   import { formatDate } from '../../utils/dateUtils.js';
   ```

4. **HTML 中的模組引用**
   ```html
   <!-- 在 HTML 文件中引用 -->
   <script type="module" src="/app/js/pages/user/newPage.js"></script>
   ```

### 新增 RPC 函數流程
1. **在 Supabase 中創建函數**
   ```sql
   CREATE OR REPLACE FUNCTION function_name(parameters)
   RETURNS return_type
   LANGUAGE plpgsql
   SECURITY DEFINER  -- 重要：使用定義者權限
   AS $$
   BEGIN
     -- 函數邏輯
     RETURN result;
   END;
   $$;
   ```

2. **在 supabaseService.js 中添加調用函數**
   ```javascript
   export async function callNewFunction(params) {
     try {
       const { data, error } = await supabase.rpc('function_name', params);
       if (error) throw error;
       return data;
     } catch (error) {
       console.error('Error calling function:', error);
       throw error;
     }
   }
   ```

3. **在頁面邏輯中使用**
   ```javascript
   import { callNewFunction } from '../../services/supabaseService.js';

   async function handleAction() {
     try {
       showLoading(true);
       const result = await callNewFunction(params);
       // 處理結果
       showToast('操作成功', 'success');
     } catch (error) {
       showToast('操作失敗：' + error.message, 'error');
     } finally {
       showLoading(false);
     }
   }
   ```

### 圖片上傳功能實現
1. **使用 cloudinaryService.js**
   ```javascript
   import { uploadImage, uploadMultipleImages } from '../../services/cloudinaryService.js';

   async function handleImageUpload(files) {
     try {
       showLoading(true);

       // 單張圖片上傳
       const result = await uploadImage(files[0], {
         folder: 'projects',
         tags: ['project', 'admin-upload']
       });

       if (result.success) {
         // 處理上傳成功
         addImagePreview(result.url);
         showToast('圖片上傳成功', 'success');
       } else {
         throw new Error(result.error);
       }
     } catch (error) {
       showToast('圖片上傳失敗：' + error.message, 'error');
     } finally {
       showLoading(false);
     }
   }
   ```

2. **批量上傳處理**
   ```javascript
   async function handleMultipleImageUpload(files) {
     const results = await uploadMultipleImages(files, options, (progress) => {
       // 更新進度顯示
       updateProgressBar(progress);
     });

     // 處理上傳結果
     results.forEach(result => {
       if (result.success) {
         addImagePreview(result.url);
       }
     });
   }
   ```

3. **圖片預覽功能**
   ```javascript
   function addImagePreview(imageUrl, filename) {
     const previewContainer = $('#imagePreviewContainer');
     const previewItem = createElement('div', { className: 'image-preview-item' });

     const img = createElement('img', {
       src: getThumbnailUrl(imageUrl, 150),
       alt: filename,
       className: 'preview-image'
     });

     const removeBtn = createElement('button', {
       type: 'button',
       className: 'preview-remove',
       innerHTML: '×',
       onclick: () => removeImagePreview(previewItem, imageUrl)
     });

     previewItem.appendChild(img);
     previewItem.appendChild(removeBtn);
     previewContainer.appendChild(previewItem);
   }
   ```

### 狀態管理使用
1. **購物車狀態管理**
   ```javascript
   import { cartStore } from '../../stores/cartStore.js';

   // 添加商品到購物車
   cartStore.addItem({
     id: 'item-123',
     name: '商品名稱',
     price: 100,
     quantity: 1
   });

   // 監聽購物車變化
   cartStore.subscribe((items) => {
     updateCartDisplay(items);
     updateCartBadge(items.length);
   });

   // 獲取購物車總數
   const totalItems = cartStore.getTotalItems();

   // 清空購物車
   cartStore.clear();
   ```

2. **認證狀態管理**
   ```javascript
   import { authStore } from '../../stores/authStore.js';

   // 設置用戶資訊
   authStore.setUser(userInfo);

   // 檢查登入狀態
   if (authStore.isLoggedIn()) {
     showUserInterface();
   } else {
     showLoginPrompt();
   }

   // 監聽認證狀態變化
   authStore.subscribe((user) => {
     if (user) {
       updateUserInterface(user);
     } else {
       showLoginForm();
     }
   });
   ```

### 錯誤處理最佳實踐
1. **統一錯誤處理**
   ```javascript
   async function performAction() {
     try {
       showLoading(true);
       const result = await apiCall();

       // 成功處理
       showToast('操作成功', 'success');
       return result;

     } catch (error) {
       console.error('Action failed:', error);

       // 根據錯誤類型顯示不同訊息
       if (error.message.includes('permission')) {
         showToast('權限不足', 'error');
       } else if (error.message.includes('network')) {
         showToast('網路連接失敗', 'error');
       } else {
         showToast('操作失敗：' + error.message, 'error');
       }

       throw error; // 重新拋出錯誤供上層處理

     } finally {
       showLoading(false);
     }
   }
   ```

2. **表單驗證**
   ```javascript
   function validateForm(formData) {
     const errors = [];

     if (!formData.name) {
       errors.push('名稱為必填項目');
     }

     if (!formData.email || !isValidEmail(formData.email)) {
       errors.push('請輸入有效的電子郵件');
     }

     if (errors.length > 0) {
       showToast('表單驗證失敗：' + errors.join(', '), 'error');
       return false;
     }

     return true;
   }

   function isValidEmail(email) {
     const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
     return emailRegex.test(email);
   }
   ```

3. **API 錯誤處理**
   ```javascript
   async function handleApiError(error) {
     console.error('API Error:', error);

     // Supabase 特定錯誤處理
     if (error.code === 'PGRST116') {
       showToast('找不到指定的資料', 'error');
     } else if (error.code === '42501') {
       showToast('權限不足，請重新登入', 'error');
       // 重定向到登入頁面
       window.location.href = '/app/admin/admin-login.html';
     } else if (error.message.includes('JWT')) {
       showToast('身份驗證過期，請重新登入', 'error');
       authStore.logout();
     } else {
       showToast('操作失敗：' + error.message, 'error');
     }
   }
   ```

### 響應式設計實現
1. **CSS 媒體查詢**
   ```css
   /* 移動優先設計 */
   .container {
     padding: 1rem;
   }

   /* 平板 */
   @media (min-width: 768px) {
     .container {
       padding: 2rem;
       max-width: 1200px;
       margin: 0 auto;
     }
   }

   /* 桌面 */
   @media (min-width: 1024px) {
     .container {
       padding: 3rem;
     }
   }
   ```

2. **JavaScript 響應式處理**
   ```javascript
   function handleResize() {
     const isMobile = window.innerWidth < 768;

     if (isMobile) {
       // 移動端特殊處理
       enableMobileNavigation();
       adjustMobileLayout();
     } else {
       // 桌面端處理
       enableDesktopNavigation();
       adjustDesktopLayout();
     }
   }

   window.addEventListener('resize', handleResize);
   handleResize(); // 初始化時執行
   ```

3. **觸控事件處理**
   ```javascript
   function initTouchEvents() {
     const elements = $$('.touch-enabled');

     elements.forEach(element => {
       // 觸控開始
       element.addEventListener('touchstart', (e) => {
         element.classList.add('touch-active');
       });

       // 觸控結束
       element.addEventListener('touchend', (e) => {
         element.classList.remove('touch-active');
       });

       // 防止雙擊縮放
       element.addEventListener('touchend', (e) => {
         e.preventDefault();
         element.click();
       });
     });
   }
   ```

## 🧪 測試策略

### 功能測試
1. **測試頁面使用**
   ```javascript
   // 在 test-orders.html 中的測試函數
   async function testOrderManagement() {
     try {
       console.log('開始測試訂單管理功能...');

       // 測試載入訂單
       const orders = await fetchAllOrdersAdmin();
       console.log('✅ 訂單載入測試通過:', orders.length);

       // 測試篩選功能
       const filteredOrders = await fetchAllOrdersAdmin({
         limit: 10,
         status: 'Pending'
       });
       console.log('✅ 訂單篩選測試通過:', filteredOrders.length);

       showToast('訂單管理測試完成', 'success');
     } catch (error) {
       console.error('❌ 訂單管理測試失敗:', error);
       showToast('測試失敗：' + error.message, 'error');
     }
   }
   ```

2. **單元測試範例**
   ```javascript
   // 測試折扣計算功能
   function testDiscountCalculation() {
     const testCases = [
       {
         items: [{ quantity: 5, price: 100 }],
         discounts: [{ min_quantity: 3, discount_percentage: 10 }],
         expected: 450 // 500 * 0.9
       },
       {
         items: [{ quantity: 2, price: 100 }],
         discounts: [{ min_quantity: 3, discount_percentage: 10 }],
         expected: 200 // 無折扣
       }
     ];

     testCases.forEach((testCase, index) => {
       const result = calculateDiscount(testCase.items, testCase.discounts);
       if (result === testCase.expected) {
         console.log(`✅ 測試案例 ${index + 1} 通過`);
       } else {
         console.error(`❌ 測試案例 ${index + 1} 失敗: 期望 ${testCase.expected}, 實際 ${result}`);
       }
     });
   }
   ```

3. **整合測試**
   ```javascript
   async function runIntegrationTests() {
     const tests = [
       { name: '管理員身份驗證', fn: testAdminAuth },
       { name: '專案管理功能', fn: testProjectManagement },
       { name: '用戶管理功能', fn: testUserManagement },
       { name: '訂單管理功能', fn: testOrderManagement },
       { name: '圖片上傳功能', fn: testImageUpload }
     ];

     for (const test of tests) {
       try {
         console.log(`開始測試: ${test.name}`);
         await test.fn();
         console.log(`✅ ${test.name} 測試通過`);
       } catch (error) {
         console.error(`❌ ${test.name} 測試失敗:`, error);
       }
     }
   }
   ```

### 效能測試
1. **載入時間監控**
   ```javascript
   function measurePageLoadTime() {
     const startTime = performance.now();

     window.addEventListener('load', () => {
       const loadTime = performance.now() - startTime;
       console.log(`頁面載入時間: ${loadTime.toFixed(2)}ms`);

       if (loadTime > 3000) {
         console.warn('頁面載入時間過長，需要優化');
       }
     });
   }
   ```

2. **API 回應時間監控**
   ```javascript
   async function monitorApiPerformance(apiCall, apiName) {
     const startTime = performance.now();

     try {
       const result = await apiCall();
       const responseTime = performance.now() - startTime;

       console.log(`${apiName} API 回應時間: ${responseTime.toFixed(2)}ms`);

       if (responseTime > 2000) {
         console.warn(`${apiName} API 回應時間過長`);
       }

       return result;
     } catch (error) {
       const responseTime = performance.now() - startTime;
       console.error(`${apiName} API 失敗 (${responseTime.toFixed(2)}ms):`, error);
       throw error;
     }
   }
   ```

## 🚀 部署指南

### 環境配置
1. **環境變數設置**
   ```bash
   # 生產環境變數
   VITE_SUPABASE_URL=https://your-project.supabase.co
   VITE_SUPABASE_ANON_KEY=your-anon-key
   VITE_LIFF_ID=your-liff-id
   VITE_CLOUDINARY_CLOUD_NAME=your-cloud-name
   VITE_CLOUDINARY_UPLOAD_PRESET=your-upload-preset
   ```

2. **Vercel 部署配置**
   ```json
   {
     "buildCommand": "npm run build",
     "outputDirectory": "dist",
     "installCommand": "npm install",
     "devCommand": "npm run dev"
   }
   ```

3. **構建優化**
   ```javascript
   // vite.config.js 生產配置
   export default defineConfig({
     build: {
       minify: 'terser',
       rollupOptions: {
         output: {
           manualChunks: {
             vendor: ['@supabase/supabase-js'],
             utils: ['./src/utils/helpers.js', './src/utils/dateUtils.js']
           }
         }
       }
     }
   });
   ```

### 部署檢查清單
1. **部署前檢查**
   - [ ] 所有環境變數正確設置
   - [ ] Supabase RPC 函數已部署
   - [ ] Cloudinary 配置正確
   - [ ] 所有測試通過
   - [ ] 程式碼已提交到 Git

2. **部署後驗證**
   - [ ] 用戶端頁面正常載入
   - [ ] 管理員登入功能正常
   - [ ] 資料庫連接正常
   - [ ] 圖片上傳功能正常
   - [ ] 所有 API 端點回應正常

3. **效能監控**
   - [ ] 頁面載入速度 < 3 秒
   - [ ] API 回應時間 < 2 秒
   - [ ] 圖片載入優化
   - [ ] 移動端體驗良好

## 🔧 故障排除指南

### 常見問題與解決方案

#### 1. Supabase 連接問題
**問題**: 無法連接到 Supabase 或 RPC 函數調用失敗
```javascript
// 診斷步驟
async function diagnoseSupabaseConnection() {
  try {
    // 檢查基本連接
    const { data, error } = await supabase.from('projects').select('count').limit(1);
    if (error) throw error;
    console.log('✅ Supabase 基本連接正常');

    // 檢查 RPC 函數
    const { data: rpcData, error: rpcError } = await supabase.rpc('get_active_projects');
    if (rpcError) throw rpcError;
    console.log('✅ RPC 函數調用正常');

  } catch (error) {
    console.error('❌ Supabase 連接問題:', error);

    // 常見解決方案
    if (error.message.includes('Invalid API key')) {
      console.log('💡 解決方案: 檢查 VITE_SUPABASE_ANON_KEY 環境變數');
    } else if (error.message.includes('function does not exist')) {
      console.log('💡 解決方案: 確認 RPC 函數已在 Supabase 中創建');
    } else if (error.code === '42501') {
      console.log('💡 解決方案: 檢查 RLS 政策和權限設置');
    }
  }
}
```

#### 2. 圖片上傳問題
**問題**: Cloudinary 圖片上傳失敗
```javascript
// 診斷 Cloudinary 配置
function diagnoseCloudinaryConfig() {
  const cloudName = import.meta.env.VITE_CLOUDINARY_CLOUD_NAME;
  const uploadPreset = import.meta.env.VITE_CLOUDINARY_UPLOAD_PRESET;

  if (!cloudName) {
    console.error('❌ 缺少 VITE_CLOUDINARY_CLOUD_NAME 環境變數');
    return false;
  }

  if (!uploadPreset) {
    console.error('❌ 缺少 VITE_CLOUDINARY_UPLOAD_PRESET 環境變數');
    return false;
  }

  console.log('✅ Cloudinary 配置檢查通過');
  return true;
}

// 測試圖片上傳
async function testImageUpload() {
  if (!diagnoseCloudinaryConfig()) return;

  try {
    // 創建測試圖片
    const canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 100;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#ff0000';
    ctx.fillRect(0, 0, 100, 100);

    canvas.toBlob(async (blob) => {
      const file = new File([blob], 'test.png', { type: 'image/png' });
      const result = await uploadImage(file);

      if (result.success) {
        console.log('✅ 圖片上傳測試成功:', result.url);
      } else {
        console.error('❌ 圖片上傳測試失敗:', result.error);
      }
    });

  } catch (error) {
    console.error('❌ 圖片上傳測試錯誤:', error);
  }
}
```

#### 3. 權限問題
**問題**: 管理員身份驗證失敗或權限不足
```javascript
// 診斷管理員權限
async function diagnoseAdminPermissions() {
  try {
    // 檢查管理員身份驗證
    const adminId = localStorage.getItem('adminId');
    if (!adminId) {
      console.error('❌ 未找到管理員 ID，請重新登入');
      return false;
    }

    // 測試管理員驗證
    const { data, error } = await supabase.rpc('admin_authenticate', { admin_id: adminId });
    if (error) throw error;

    if (data) {
      console.log('✅ 管理員身份驗證成功');
      return true;
    } else {
      console.error('❌ 管理員身份驗證失敗');
      return false;
    }

  } catch (error) {
    console.error('❌ 管理員權限檢查失敗:', error);

    if (error.code === '42883') {
      console.log('💡 解決方案: admin_authenticate RPC 函數不存在，請在 Supabase 中創建');
    } else if (error.code === '42501') {
      console.log('💡 解決方案: 檢查 RLS 政策，確保 RPC 函數有正確權限');
    }

    return false;
  }
}
```

#### 4. 模組載入問題
**問題**: ES6 模組導入失敗或找不到模組
```javascript
// 檢查模組載入
function checkModuleLoading() {
  const requiredModules = [
    '/app/js/services/supabaseService.js',
    '/app/js/services/cloudinaryService.js',
    '/app/js/utils/helpers.js',
    '/app/js/stores/cartStore.js'
  ];

  requiredModules.forEach(async (modulePath) => {
    try {
      await import(modulePath);
      console.log(`✅ 模組載入成功: ${modulePath}`);
    } catch (error) {
      console.error(`❌ 模組載入失敗: ${modulePath}`, error);

      if (error.message.includes('404')) {
        console.log(`💡 解決方案: 檢查文件路徑是否正確`);
      } else if (error.message.includes('SyntaxError')) {
        console.log(`💡 解決方案: 檢查模組語法錯誤`);
      }
    }
  });
}
```

### 除錯工具和技巧

#### 1. 瀏覽器開發者工具使用
```javascript
// 啟用詳細日誌
function enableDebugMode() {
  window.DEBUG = true;

  // 攔截所有 console.log
  const originalLog = console.log;
  console.log = function(...args) {
    originalLog.apply(console, ['[DEBUG]', new Date().toISOString(), ...args]);
  };

  // 攔截所有 fetch 請求
  const originalFetch = window.fetch;
  window.fetch = function(...args) {
    console.log('🌐 API 請求:', args[0]);
    return originalFetch.apply(this, args)
      .then(response => {
        console.log('📥 API 回應:', response.status, response.statusText);
        return response;
      })
      .catch(error => {
        console.error('❌ API 錯誤:', error);
        throw error;
      });
  };
}
```

#### 2. 效能分析
```javascript
// 效能監控工具
class PerformanceMonitor {
  constructor() {
    this.metrics = {};
  }

  start(name) {
    this.metrics[name] = { start: performance.now() };
  }

  end(name) {
    if (this.metrics[name]) {
      this.metrics[name].end = performance.now();
      this.metrics[name].duration = this.metrics[name].end - this.metrics[name].start;
      console.log(`⏱️ ${name}: ${this.metrics[name].duration.toFixed(2)}ms`);
    }
  }

  report() {
    console.table(this.metrics);
  }
}

// 使用範例
const monitor = new PerformanceMonitor();
monitor.start('loadProjects');
// ... 執行載入專案的代碼
monitor.end('loadProjects');
```

## 📚 最佳實踐總結

### 程式碼品質
1. **命名規範**
   - 使用有意義的變數和函數名稱
   - 遵循 camelCase 命名慣例
   - 常數使用 UPPER_SNAKE_CASE

2. **程式碼組織**
   - 每個文件專注於單一職責
   - 使用 ES6 模組進行代碼分離
   - 保持函數簡短和專注

3. **註解和文檔**
   ```javascript
   /**
    * 計算訂單總金額（包含折扣）
    * @param {Array} items - 訂單項目列表
    * @param {Array} discounts - 可用折扣列表
    * @returns {number} 計算後的總金額
    */
   function calculateOrderTotal(items, discounts) {
     // 實現邏輯...
   }
   ```

### 安全性最佳實踐
1. **輸入驗證**
   - 始終驗證用戶輸入
   - 使用白名單而非黑名單
   - 防止 XSS 攻擊

2. **權限控制**
   - 最小權限原則
   - 定期檢查權限設置
   - 使用 RLS 保護敏感資料

3. **資料保護**
   - 不在前端儲存敏感資訊
   - 使用 HTTPS 傳輸
   - 定期更新依賴套件

### 效能優化
1. **載入優化**
   - 延遲載入非關鍵資源
   - 壓縮圖片和資源
   - 使用 CDN 加速

2. **程式碼優化**
   - 避免不必要的 DOM 操作
   - 使用事件委託
   - 實現適當的快取策略

3. **監控和分析**
   - 定期檢查效能指標
   - 使用瀏覽器效能工具
   - 監控 API 回應時間

---

## 📊 當前開發狀態 (2025/05/25 更新)

### ✅ 已完成的核心功能

#### 專案管理系統 (100% 完成)
- ✅ **基本 CRUD 操作**：創建、讀取、更新、刪除專案
- ✅ **編輯功能完全修正**：
  - 解決 RPC 函數資料結構解析問題，正確提取嵌套資料
  - 解決表單資料載入時序問題，確保所有欄位正確填充
  - 專案名稱、描述、狀態等所有基本欄位正確載入
- ✅ **刪除功能修正**：
  - 修正資料庫約束，添加 `deleted` 狀態支援
  - 修正觸發器函數，允許手動設置刪除狀態
  - 實現正確的軟刪除功能
- ✅ **圖片上傳整合**：Cloudinary 多圖片上傳、預覽、管理
- ✅ **表單驗證**：完整的前端和後端驗證
- ✅ **狀態管理**：專案狀態自動更新機制

#### 用戶管理系統 (100% 完成)
- ✅ **完整 CRUD 操作**：用戶創建、查詢、更新、軟刪除
- ✅ **角色管理**：管理員、一般用戶角色控制
- ✅ **安全保護**：防止管理員帳戶被誤刪

#### 基礎架構 (100% 完成)
- ✅ **Supabase 整合**：RPC 函數、RLS 政策、觸發器
- ✅ **Cloudinary 服務**：圖片上傳、壓縮、CDN
- ✅ **認證系統**：管理員身份驗證
- ✅ **UI/UX 系統**：Toast 通知、載入狀態、響應式設計

### 🔄 當前進行中的功能

#### 專案管理功能完善 (80% 完成)
- ✅ 基本 CRUD 操作
- ✅ 編輯表單資料載入修正
- ✅ 刪除功能核心問題修正
- 🔄 專案亮點管理功能實現 (框架已建立，待完善)
- 🔄 折扣規則完整整合 (基礎架構完成，待前端整合)
- 🔄 商品清單上傳和解析功能 (UI 已建立，待功能實現)

### 🎯 下一步開發優先級

#### 1. 專案管理功能完善 (剩餘 20%)
**預估時間**: 1-2 天
- [ ] **專案亮點管理**：
  - 實現動態添加/編輯/刪除亮點功能
  - 支援 Emoji 圖示選擇
  - 最多 3 個亮點限制

- [ ] **折扣規則整合**：
  - 完善新折扣創建流程
  - 整合現有折扣選擇功能
  - 折扣規則驗證和預覽

- [ ] **商品清單功能**：
  - Excel/CSV 文件上傳
  - 商品資料解析和驗證
  - 商品清單預覽和編輯

#### 2. 訂單管理系統完善 (優先級：高)
**預估時間**: 3-4 天
- [ ] **訂單詳情彈窗**：
  - 完整的訂單資訊顯示
  - 訂單項目詳細列表
  - 用戶資訊和聯絡方式

- [ ] **訂單狀態管理**：
  - 狀態更新流程優化
  - 批量狀態更新功能
  - 狀態變更歷史記錄

- [ ] **訂單與專案關聯**：
  - 訂單-專案關聯查詢
  - 專案訂單統計
  - 交叉引用功能

#### 3. 商品管理功能 (優先級：高)
**預估時間**: 2-3 天
- [ ] **商品 CRUD 操作**：
  - 商品創建、編輯、刪除
  - 商品分類和標籤
  - 商品圖片管理

- [ ] **庫存管理**：
  - 庫存數量追蹤
  - 庫存警告機制
  - 庫存歷史記錄

- [ ] **價格管理**：
  - 基礎價格設定
  - 折扣價格計算
  - 價格歷史追蹤

#### 4. 前端用戶介面 (優先級：中)
**預估時間**: 4-5 天
- [ ] **用戶端專案瀏覽**：
  - 專案列表頁面
  - 專案詳情頁面
  - 商品選擇介面

- [ ] **購物車系統**：
  - 購物車狀態管理
  - 商品數量調整
  - 折扣計算顯示

- [ ] **訂購流程**：
  - 用戶資訊填寫
  - 訂單確認頁面
  - 訂單提交處理

### 🔧 技術債務和優化

#### 需要處理的技術問題
1. **認證系統改進**：
   - 實現真正的 JWT 認證
   - 添加認證過期處理
   - 改善權限檢查機制

2. **錯誤處理優化**：
   - 統一錯誤訊息格式
   - 改善網路錯誤處理
   - 添加重試機制

3. **效能優化**：
   - 實現資料分頁載入
   - 添加圖片懶載入
   - 優化 RPC 函數查詢

### 📋 開發接續指南

當重新開始開發時，請按照以下步驟：

1. **環境檢查**
   - 確認 Node.js 版本 (18+)
   - 檢查環境變數設置 (.env.local)
   - 測試 Supabase 連接：`http://localhost:3001/app/admin/auth-test.html`

2. **功能驗證**
   - 測試專案管理功能：`http://localhost:3001/app/admin/management/projects.html`
   - 測試用戶管理功能：`http://localhost:3001/app/admin/management/user-management.html`
   - 驗證圖片上傳功能：專案管理頁面的圖片上傳

3. **開發準備**
   - 查看 README.md 了解最新功能狀態
   - 檢查本文件的下一步優先級
   - 準備開發工具和瀏覽器開發者工具

4. **開發流程**
   - 從專案管理功能完善開始 (剩餘 20%)
   - 按照優先級順序進行開發
   - 每完成一個功能模組就進行測試和文檔更新

### 📈 預估開發時程

- **專案管理功能完善**: 1-2 天
- **訂單管理系統完善**: 3-4 天
- **商品管理功能**: 2-3 天
- **前端用戶介面**: 4-5 天
- **技術債務處理**: 2-3 天

**總計**: 約 12-17 天完成核心功能開發

這份完整的開發指引提供了系統開發所需的所有技術細節和最佳實踐，確保開發工作的連續性和品質。
