<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug 測試頁面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .test-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .test-button {
            background-color: #57AC5A;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .test-result {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin-top: 12px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .success {
            border-color: #57AC5A;
            background-color: #f0f8f0;
            color: #2d5a2f;
        }

        .error {
            border-color: #e74c3c;
            background-color: #fdf2f2;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Debug 測試頁面</h1>

    <!-- 專案創建測試 -->
    <div class="test-container">
        <h2>專案創建測試</h2>
        <button class="test-button" id="test-create-project">測試創建專案</button>
        <div class="test-result" id="project-result">點擊按鈕開始測試...</div>
    </div>

    <!-- 用戶查詢測試 -->
    <div class="test-container">
        <h2>用戶查詢測試</h2>
        <button class="test-button" id="test-fetch-users">測試查詢用戶</button>
        <div class="test-result" id="user-result">點擊按鈕開始測試...</div>
    </div>

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <script type="module">
        import {
            createProject,
            fetchAllUsers,
            authenticateAsAdmin,
            initializeSupabase
        } from '../js/services/supabaseService.js';

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            initializeSupabase();
        });

        // 測試創建專案
        async function testCreateProject() {
            const resultDiv = document.getElementById('project-result');
            resultDiv.textContent = '正在測試創建專案...';
            resultDiv.className = 'test-result';

            try {
                // 確保管理員身份驗證
                await authenticateAsAdmin();

                const projectData = {
                    name: `Debug 測試專案 ${new Date().toLocaleString()}`,
                    description: '這是一個 Debug 測試專案',
                    status: 'active',
                    images: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
                    highlights: ['測試亮點1', '測試亮點2'],
                    items: [],
                    discounts: []
                };

                console.log('Creating project with data:', projectData);
                const result = await createProject(projectData);
                
                resultDiv.textContent = `✅ 專案創建成功！\n\n專案ID: ${result}\n專案名稱: ${projectData.name}\n圖片數量: ${projectData.images.length}`;
                resultDiv.className = 'test-result success';
            } catch (error) {
                console.error('Create project error:', error);
                resultDiv.textContent = `❌ 創建專案失敗：\n\n${error.message}\n\n詳細錯誤：${JSON.stringify(error, null, 2)}`;
                resultDiv.className = 'test-result error';
            }
        }

        // 測試查詢用戶
        async function testFetchUsers() {
            const resultDiv = document.getElementById('user-result');
            resultDiv.textContent = '正在測試查詢用戶...';
            resultDiv.className = 'test-result';

            try {
                // 確保管理員身份驗證
                await authenticateAsAdmin();

                console.log('Fetching users...');
                const result = await fetchAllUsers({}, { page: 1, limit: 5 });
                
                if (result.users && result.users.length > 0) {
                    const userSummary = result.users.map(user =>
                        `ID: ${user.id}\n顯示名稱: ${user.display_name || '未設定'}\n角色: ${user.role || 'user'}\n創建時間: ${user.created_at}`
                    ).join('\n\n');

                    resultDiv.textContent = `✅ 用戶查詢成功！\n\n找到 ${result.users.length} 個用戶（共 ${result.totalCount} 個）：\n\n${userSummary}`;
                    resultDiv.className = 'test-result success';
                } else {
                    resultDiv.textContent = '⚠️ 沒有找到用戶資料';
                    resultDiv.className = 'test-result';
                }
            } catch (error) {
                console.error('Fetch users error:', error);
                resultDiv.textContent = `❌ 查詢用戶失敗：\n\n${error.message}\n\n詳細錯誤：${JSON.stringify(error, null, 2)}`;
                resultDiv.className = 'test-result error';
            }
        }

        // 綁定事件
        document.getElementById('test-create-project').addEventListener('click', testCreateProject);
        document.getElementById('test-fetch-users').addEventListener('click', testFetchUsers);
    </script>
</body>
</html>
